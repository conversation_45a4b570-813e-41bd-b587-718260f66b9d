directories:
  output: build-output
  buildResources: assets
appId: com.codegenius.app
productName: Code Genius
files:
  - filter:
      - dist/**/*
      - dist-electron/**/*
      - package.json
      - electron/**/*
asar: true
compression: maximum
generateUpdatesFilesForAllChannels: true
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
forceCodeSigning: false
mac:
  category: public.app-category.developer-tools
  target:
    - target: dmg
      arch:
        - x64
        - arm64
    - target: zip
      arch:
        - x64
        - arm64
  artifactName: CodeGenius-${arch}.${ext}
  icon: assets/icons/mac/icon.icns
  hardenedRuntime: true
  gatekeeperAssess: false
  entitlements: build/entitlements.mac.plist
  entitlementsInherit: build/entitlements.mac.plist
  identity: Developer ID Application
  notarize: true
  protocols:
    name: code-genius-protocol
    schemes:
      - code-genius
win:
  target:
    - target: dir
      arch:
        - x64
  icon: assets/icons/win/icon.ico
  artifactName: ${productName}-Windows-${version}.${ext}
  protocols:
    name: code-genius-protocol
    schemes:
      - code-genius
linux:
  target:
    - AppImage
  icon: assets/icons/png/icon-256x256.png
  artifactName: ${productName}-Linux-${version}.${ext}
  protocols:
    name: code-genius-protocol
    schemes:
      - code-genius
publish:
  - provider: github
    owner: your-username
    repo: code-genius
    private: false
    releaseType: release
extraResources:
  - from: .env
    to: .env
    filter:
      - '**/*'
extraMetadata:
  main: dist-electron/main.js
electronVersion: 29.4.6
