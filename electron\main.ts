import { app, BrowserWindow, screen, shell, ipcMain, desktopCapturer, session } from "electron"
import path from "path"
import { initializeIpcHandlers } from "./ipcHandlers"
import { ProcessingHelper } from "./ProcessingHelper"
import { ScreenshotHelper } from "./ScreenshotHelper"
import { ShortcutsHelper } from "./shortcuts"
import { initAutoUpdater } from "./autoUpdater"
import { PaymentService } from "./PaymentService"
import { GeminiLiveService } from "./GeminiLiveService"
import { DeepgramService } from "./DeepgramService"
import * as dotenv from "dotenv"

// Constants
const isDev = !app.isPackaged

// Application State
const state = {
  // Window management properties
  mainWindow: null as BrowserWindow | null,
  isWindowVisible: false,
  windowPosition: null as { x: number; y: number } | null,
  windowSize: null as { width: number; height: number } | null,
  screenWidth: 0,
  screenHeight: 0,
  step: 0,
  currentX: 0,
  currentY: 0,

  // Application helpers
  screenshotHelper: null as ScreenshotHelper | null,
  shortcutsHelper: null as ShortcutsHelper | null,
  processingHelper: null as ProcessingHelper | null,
  paymentService: null as PaymentService | null,
  geminiLiveService: null as GeminiLiveService | null,

  // View and state management
  view: "queue" as "queue" | "solutions" | "debug" | "voice",
  problemInfo: null as any,
  hasDebugged: false,

  // Processing events
  PROCESSING_EVENTS: {
    UNAUTHORIZED: "processing-unauthorized",
    NO_SCREENSHOTS: "processing-no-screenshots",
    OUT_OF_CREDITS: "out-of-credits",
    API_KEY_INVALID: "processing-api-key-invalid",
    INITIAL_START: "initial-start",
    PROBLEM_EXTRACTED: "problem-extracted",
    SOLUTION_SUCCESS: "solution-success",
    INITIAL_SOLUTION_ERROR: "solution-error",
    DEBUG_START: "debug-start",
    DEBUG_SUCCESS: "debug-success",
    DEBUG_ERROR: "debug-error"
  } as const
}

// Add interfaces for helper classes
export interface IProcessingHelperDeps {
  getScreenshotHelper: () => ScreenshotHelper | null
  getMainWindow: () => BrowserWindow | null
  getView: () => "queue" | "solutions" | "debug"
  setView: (view: "queue" | "solutions" | "debug") => void
  getProblemInfo: () => any
  setProblemInfo: (info: any) => void
  getScreenshotQueue: () => string[]
  getExtraScreenshotQueue: () => string[]
  clearQueues: () => void
  takeScreenshot: () => Promise<string>
  getImagePreview: (filepath: string) => Promise<string>
  deleteScreenshot: (
    path: string
  ) => Promise<{ success: boolean; error?: string }>
  setHasDebugged: (value: boolean) => void
  getHasDebugged: () => boolean
  PROCESSING_EVENTS: typeof state.PROCESSING_EVENTS
}

export interface IShortcutsHelperDeps {
  getMainWindow: () => BrowserWindow | null
  takeScreenshot: () => Promise<string>
  getImagePreview: (filepath: string) => Promise<string>
  processingHelper: ProcessingHelper | null
  clearQueues: () => void
  setView: (view: "queue" | "solutions" | "debug" | "voice") => void
  isVisible: () => boolean
  toggleMainWindow: () => void
  moveWindowLeft: () => void
  moveWindowRight: () => void
  moveWindowUp: () => void
  moveWindowDown: () => void
}

export interface IIpcHandlerDeps {
  getMainWindow: () => BrowserWindow | null
  setWindowDimensions: (width: number, height: number) => void
  getScreenshotQueue: () => string[]
  getExtraScreenshotQueue: () => string[]
  deleteScreenshot: (
    path: string
  ) => Promise<{ success: boolean; error?: string }>
  getImagePreview: (filepath: string) => Promise<string>
  processingHelper: ProcessingHelper | null
  paymentService: PaymentService | null
  geminiLiveService: GeminiLiveService | null
  PROCESSING_EVENTS: typeof state.PROCESSING_EVENTS
  takeScreenshot: () => Promise<string>
  getView: () => "queue" | "solutions" | "debug" | "voice"
  toggleMainWindow: () => void
  clearQueues: () => void
  setView: (view: "queue" | "solutions" | "debug" | "voice") => void
  moveWindowLeft: () => void
  moveWindowRight: () => void
  moveWindowUp: () => void
  moveWindowDown: () => void
}

// Initialize helpers
function initializeHelpers() {
  state.paymentService = new PaymentService()
  state.screenshotHelper = new ScreenshotHelper(state.view)

  // Initialize Gemini Live Service with dedicated API key from environment
  const regularApiKey = process.env.GEMINI_API_KEY || '';
  const liveApiKey = process.env.GEMINI_LIVE_API_KEY || '';
  const deepgramApiKey = process.env.DEEPGRAM_API_KEY || '';

  console.log('DEBUG: Regular Gemini API key exists:', !!regularApiKey);
  console.log('DEBUG: Gemini Live API key exists:', !!liveApiKey);
  console.log('DEBUG: Deepgram API key exists:', !!deepgramApiKey);

  if (!liveApiKey && !regularApiKey) {
    console.error('DEBUG: No API keys found in environment variables');
  } else {
    console.log('DEBUG: Initializing Gemini Live Service');
    console.log('DEBUG: Live API key length:', liveApiKey ? liveApiKey.length : 0);
    console.log('DEBUG: Regular API key length:', regularApiKey ? regularApiKey.length : 0);
    console.log('DEBUG: Deepgram API key length:', deepgramApiKey ? deepgramApiKey.length : 0);

    // Initialize with all keys - live API key as primary, regular API key as fallback, and Deepgram for speech-to-text
    state.geminiLiveService = new GeminiLiveService(liveApiKey, state.mainWindow, regularApiKey, deepgramApiKey);

    // Validate the API key
    setTimeout(async () => {
      try {
        console.log('DEBUG: Validating Gemini Live API key...');
        const isValid = await state.geminiLiveService?.validateApiKey();
        console.log('DEBUG: API key validation result:', isValid);

        if (isValid) {
          console.log('DEBUG: API key is valid, initializing session');
          // Initialize the session
          const sessionInitialized = await state.geminiLiveService?.initSession();
          console.log('DEBUG: Session initialization result:', sessionInitialized);

          if (sessionInitialized) {
            if (state.mainWindow) {
              state.mainWindow.webContents.send('gemini-live-status', {
                connected: true,
                message: 'Gemini Live API connected successfully'
              });
            }
          } else {
            console.error('DEBUG: Failed to initialize session');
            if (state.mainWindow) {
              state.mainWindow.webContents.send('gemini-live-error', {
                error: 'Failed to initialize session',
                details: 'API connection could not be established'
              });
            }
          }
        } else {
          console.error('DEBUG: Invalid Gemini API keys, voice-to-text functionality will not work');
          if (state.mainWindow) {
            state.mainWindow.webContents.send('gemini-api-error', {
              error: 'Invalid Gemini API keys',
              details: 'Please check your API keys in the .env file'
            });
          }
        }
      } catch (error) {
        console.error('DEBUG: Error validating API key:', error);
      }
    }, 5000); // Wait 5 seconds after app startup to validate
  }

  state.processingHelper = new ProcessingHelper({
    getScreenshotHelper,
    getMainWindow,
    getView,
    setView,
    getProblemInfo,
    setProblemInfo,
    getScreenshotQueue,
    getExtraScreenshotQueue,
    clearQueues,
    takeScreenshot,
    getImagePreview,
    deleteScreenshot,
    setHasDebugged,
    getHasDebugged,
    PROCESSING_EVENTS: state.PROCESSING_EVENTS
  } as IProcessingHelperDeps)
  state.shortcutsHelper = new ShortcutsHelper({
    getMainWindow,
    takeScreenshot,
    getImagePreview,
    processingHelper: state.processingHelper,
    clearQueues,
    setView,
    isVisible: () => state.isWindowVisible,
    toggleMainWindow,
    moveWindowLeft: () =>
      moveWindowHorizontal((x) =>
        Math.max(-(state.windowSize?.width || 0) / 2, x - state.step)
      ),
    moveWindowRight: () =>
      moveWindowHorizontal((x) =>
        Math.min(
          state.screenWidth - (state.windowSize?.width || 0) / 2,
          x + state.step
        )
      ),
    moveWindowUp: () => moveWindowVertical((y) => y - state.step),
    moveWindowDown: () => moveWindowVertical((y) => y + state.step)
  } as IShortcutsHelperDeps)
}

// Auth callback handler

// Register the code-genius protocol
if (process.platform === "darwin") {
  app.setAsDefaultProtocolClient("code-genius")
} else {
  app.setAsDefaultProtocolClient("code-genius", process.execPath, [
    path.resolve(process.argv[1] || "")
  ])
}

// Handle the protocol. In this case, we choose to show an Error Box.
if (process.defaultApp && process.argv.length >= 2) {
  app.setAsDefaultProtocolClient("code-genius", process.execPath, [
    path.resolve(process.argv[1])
  ])
}

// Force Single Instance Lock
const gotTheLock = app.requestSingleInstanceLock()

if (!gotTheLock) {
  app.quit()
} else {
  app.on("second-instance", (event, commandLine) => {
    // Someone tried to run a second instance, we should focus our window.
    if (state.mainWindow) {
      if (state.mainWindow.isMinimized()) state.mainWindow.restore()
      state.mainWindow.focus()

      // Protocol handler for state.mainWindow32
      // argv: An array of the second instance's (command line / deep linked) arguments
      if (process.platform === "win32") {
        // Keep only command line / deep linked arguments
        const deeplinkingUrl = commandLine.pop()
        if (deeplinkingUrl) {
          handleAuthCallback(deeplinkingUrl, state.mainWindow)
        }
      }
    }
  })
}

async function handleAuthCallback(url: string, win: BrowserWindow | null) {
  try {
    console.log("Auth callback received:", url)
    const urlObj = new URL(url)
    const code = urlObj.searchParams.get("code")

    if (!code) {
      console.error("Missing code in callback URL")
      return
    }

    if (win) {
      // Send the code to the renderer for PKCE exchange
      win.webContents.send("auth-callback", { code })
    }
  } catch (error) {
    console.error("Error handling auth callback:", error)
  }
}

// Window management functions
async function createWindow(): Promise<void> {
  if (state.mainWindow) {
    if (state.mainWindow.isMinimized()) state.mainWindow.restore()
    state.mainWindow.focus()
    return
  }

  const primaryDisplay = screen.getPrimaryDisplay()
  const workArea = primaryDisplay.workAreaSize
  state.screenWidth = workArea.width
  state.screenHeight = workArea.height
  state.step = 60
  state.currentY = 50

  const windowSettings: Electron.BrowserWindowConstructorOptions = {
    height: 600,

    x: state.currentX,
    y: 50,
    alwaysOnTop: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: isDev
        ? path.join(__dirname, "../dist-electron/preload.js")
        : path.join(__dirname, "preload.js"),
      scrollBounce: true
    },
    show: true,
    frame: false,
    transparent: true,
    fullscreenable: false,
    hasShadow: false,
    backgroundColor: "#00000000",
    focusable: true,
    skipTaskbar: true,
    type: "panel",
    paintWhenInitiallyHidden: true,
    titleBarStyle: "hidden",
    enableLargerThanScreen: true,
    movable: true
  }

  state.mainWindow = new BrowserWindow(windowSettings)

  // Add more detailed logging for window events
  state.mainWindow.webContents.on("did-finish-load", () => {
    console.log("Window finished loading")
  })
  state.mainWindow.webContents.on(
    "did-fail-load",
    async (event, errorCode, errorDescription) => {
      console.error("Window failed to load:", errorCode, errorDescription)
      if (isDev) {
        // In development, retry loading after a short delay
        console.log("Retrying to load development server...")
        setTimeout(() => {
          state.mainWindow?.loadURL("http://localhost:54321").catch((error) => {
            console.error("Failed to load dev server on retry:", error)
          })
        }, 1000)
      }
    }
  )

  if (isDev) {
    // In development, load from the dev server
    state.mainWindow.loadURL("http://localhost:54321").catch((error) => {
      console.error("Failed to load dev server:", error)
    })
  } else {
    // In production, load from the built files
    console.log(
      "Loading production build:",
      path.join(__dirname, "../dist/index.html")
    )
    state.mainWindow.loadFile(path.join(__dirname, "../dist/index.html"))
  }

  // Configure window behavior
  state.mainWindow.webContents.setZoomFactor(1)
  // Only open DevTools in development mode when explicitly requested
  if (isDev && process.env.OPEN_DEV_TOOLS === 'true') {
    state.mainWindow.webContents.openDevTools()
  }
  state.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    console.log("Attempting to open URL:", url)
    if (url.includes("google.com") || url.includes("supabase.co")) {
      shell.openExternal(url)
      return { action: "deny" }
    }
    return { action: "allow" }
  })

  // Set up display media request handler to enable system audio capture
  session.defaultSession.setDisplayMediaRequestHandler((_request, callback) => {
    console.log('Display media request received');

    try {
      // Get available screen sources for audio only
      desktopCapturer.getSources({ types: ['screen'] })
        .then(sources => {
          if (sources.length > 0) {
            // Use a more reliable approach - just request system audio
            console.log(`Using source: ${sources[0].name} for display capture (audio only)`);
            callback({
              audio: 'loopback', // This enables system audio capture
              video: sources[0]  // We need to provide a source, but we'll disable it in the renderer
            });
            console.log('Requested system audio capture without video');
          } else {
            console.error('No screen sources found');
            callback(null);
          }
        })
        .catch(error => {
          console.error('Error getting screen sources:', error);
          callback(null);
        });
    } catch (error) {
      console.error('Error setting up display media:', error);
      callback(null);
    }
  });

  // Advanced screen capture resistance and anti-detection features
  state.mainWindow.setContentProtection(true)

  state.mainWindow.setVisibleOnAllWorkspaces(true, {
    visibleOnFullScreen: true
  })
  state.mainWindow.setAlwaysOnTop(true, "screen-saver", 1)

  // Additional screen capture resistance settings
  if (process.platform === "darwin") {
    // Prevent window from being captured in screenshots
    state.mainWindow.setHiddenInMissionControl(true)
    state.mainWindow.setWindowButtonVisibility(false)
    state.mainWindow.setBackgroundColor("#00000000")

    // Prevent window from being included in window switcher
    state.mainWindow.setSkipTaskbar(true)

    // Disable window shadow
    state.mainWindow.setHasShadow(false)
  }

  // Advanced anti-detection features
  // Randomize window position slightly on each show to avoid detection patterns
  const randomizePosition = () => {
    if (state.mainWindow && state.isWindowVisible) {
      const [x, y] = state.mainWindow.getPosition();
      const offsetX = Math.floor(Math.random() * 5) - 2; // -2 to +2 pixels
      const offsetY = Math.floor(Math.random() * 5) - 2; // -2 to +2 pixels
      state.mainWindow.setPosition(x + offsetX, y + offsetY);
    }
  };

  // Set up periodic position randomization
  setInterval(randomizePosition, 30000); // Every 30 seconds

  // Detect screen recording software
  const detectScreenRecording = () => {
    if (process.platform === 'darwin') {
      try {
        // On macOS, we can use the systemPreferences API to detect screen recording
        const isBeingCaptured = state.mainWindow?.webContents.isBeingCaptured();
        if (isBeingCaptured) {
          console.log('Screen recording detected, taking evasive action');
          // Hide the window temporarily
          hideMainWindow();
          // Show it again after a random delay
          setTimeout(() => {
            showMainWindow();
          }, 1000 + Math.random() * 2000);
        }
      } catch (error) {
        console.error('Error detecting screen recording:', error);
      }
    }
  };

  // Check for screen recording periodically
  setInterval(detectScreenRecording, 5000); // Every 5 seconds

  // Prevent the window from being captured by screen recording
  state.mainWindow.webContents.setBackgroundThrottling(false)
  state.mainWindow.webContents.setFrameRate(60)

  // Set up window listeners
  state.mainWindow.on("move", handleWindowMove)
  state.mainWindow.on("resize", handleWindowResize)
  state.mainWindow.on("closed", handleWindowClosed)

  // Initialize window state
  const bounds = state.mainWindow.getBounds()
  state.windowPosition = { x: bounds.x, y: bounds.y }
  state.windowSize = { width: bounds.width, height: bounds.height }
  state.currentX = bounds.x
  state.currentY = bounds.y
  state.isWindowVisible = true

  // Set up IPC listener for resume and job description updates
  state.mainWindow.webContents.on('ipc-message', (event, channel, ...args) => {
    if (channel === 'update-resume-job-info' && args.length >= 2) {
      const [resumeInfo, jobDescription] = args;
      console.log('Received resume and job info update via IPC');
      console.log('Resume info length:', resumeInfo?.length || 0);
      console.log('Job description length:', jobDescription?.length || 0);

      // Update global variables
      (global as any).resumeInfo = resumeInfo || '';
      (global as any).jobDescription = jobDescription || '';

      console.log('Resume and job info updated in main process');
    }
  })
}

function handleWindowMove(): void {
  if (!state.mainWindow) return
  const bounds = state.mainWindow.getBounds()
  state.windowPosition = { x: bounds.x, y: bounds.y }
  state.currentX = bounds.x
  state.currentY = bounds.y
}

function handleWindowResize(): void {
  if (!state.mainWindow) return
  const bounds = state.mainWindow.getBounds()
  state.windowSize = { width: bounds.width, height: bounds.height }
}

function handleWindowClosed(): void {
  state.mainWindow = null
  state.isWindowVisible = false
  state.windowPosition = null
  state.windowSize = null
}

// Window visibility functions
function hideMainWindow(): void {
  if (!state.mainWindow?.isDestroyed()) {
    const bounds = state.mainWindow.getBounds()
    state.windowPosition = { x: bounds.x, y: bounds.y }
    state.windowSize = { width: bounds.width, height: bounds.height }
    state.mainWindow.setIgnoreMouseEvents(true, { forward: true })
    state.mainWindow.setAlwaysOnTop(true, "screen-saver", 1)
    state.mainWindow.setVisibleOnAllWorkspaces(true, {
      visibleOnFullScreen: true
    })
    state.mainWindow.setOpacity(0)
    state.mainWindow.hide()
    state.isWindowVisible = false
  }
}

function showMainWindow(): void {
  if (!state.mainWindow?.isDestroyed()) {
    if (state.windowPosition && state.windowSize) {
      state.mainWindow.setBounds({
        ...state.windowPosition,
        ...state.windowSize
      })
    }
    state.mainWindow.setIgnoreMouseEvents(false)
    state.mainWindow.setAlwaysOnTop(true, "screen-saver", 1)
    state.mainWindow.setVisibleOnAllWorkspaces(true, {
      visibleOnFullScreen: true
    })
    state.mainWindow.setContentProtection(true)
    state.mainWindow.setOpacity(0)
    state.mainWindow.showInactive()
    state.mainWindow.setOpacity(1)
    state.isWindowVisible = true
  }
}

function toggleMainWindow(): void {
  state.isWindowVisible ? hideMainWindow() : showMainWindow()
}

// Window movement functions
function moveWindowHorizontal(updateFn: (x: number) => number): void {
  if (!state.mainWindow) return
  state.currentX = updateFn(state.currentX)
  state.mainWindow.setPosition(
    Math.round(state.currentX),
    Math.round(state.currentY)
  )
}

function moveWindowVertical(updateFn: (y: number) => number): void {
  if (!state.mainWindow) return

  const newY = updateFn(state.currentY)
  // Allow window to go 2/3 off screen in either direction
  const maxUpLimit = (-(state.windowSize?.height || 0) * 2) / 3
  const maxDownLimit =
    state.screenHeight + ((state.windowSize?.height || 0) * 2) / 3

  // Log the current state and limits
  console.log({
    newY,
    maxUpLimit,
    maxDownLimit,
    screenHeight: state.screenHeight,
    windowHeight: state.windowSize?.height,
    currentY: state.currentY
  })

  // Only update if within bounds
  if (newY >= maxUpLimit && newY <= maxDownLimit) {
    state.currentY = newY
    state.mainWindow.setPosition(
      Math.round(state.currentX),
      Math.round(state.currentY)
    )
  }
}

// Window dimension functions
function setWindowDimensions(width: number, height: number): void {
  if (!state.mainWindow?.isDestroyed()) {
    const [currentX, currentY] = state.mainWindow.getPosition()
    const primaryDisplay = screen.getPrimaryDisplay()
    const workArea = primaryDisplay.workAreaSize
    const maxWidth = Math.floor(workArea.width * 0.5)

    state.mainWindow.setBounds({
      x: Math.min(currentX, workArea.width - maxWidth),
      y: currentY,
      width: Math.min(width + 32, maxWidth),
      height: Math.ceil(height)
    })
  }
}

// Environment setup
function loadEnvVariables() {
  if (isDev) {
    console.log("Loading env variables from:", path.join(process.cwd(), ".env"))
    dotenv.config({ path: path.join(process.cwd(), ".env") })
  } else {
    console.log(
      "Loading env variables from:",
      path.join(process.resourcesPath, ".env")
    )
    dotenv.config({ path: path.join(process.resourcesPath, ".env") })
  }
  console.log("Loaded environment variables:", {
    VITE_SUPABASE_URL: process.env.VITE_SUPABASE_URL ? "exists" : "missing",
    VITE_SUPABASE_ANON_KEY: process.env.VITE_SUPABASE_ANON_KEY
      ? "exists"
      : "missing"
  })
}

// Initialize application
async function initializeApp() {
  try {
    loadEnvVariables()
    initializeHelpers()
    initializeIpcHandlers({
      getMainWindow,
      setWindowDimensions,
      getScreenshotQueue,
      getExtraScreenshotQueue,
      deleteScreenshot,
      getImagePreview,
      processingHelper: state.processingHelper,
      paymentService: state.paymentService,
      geminiLiveService: state.geminiLiveService,
      PROCESSING_EVENTS: state.PROCESSING_EVENTS,
      takeScreenshot,
      getView,
      toggleMainWindow,
      clearQueues,
      setView,
      moveWindowLeft: () =>
        moveWindowHorizontal((x) =>
          Math.max(-(state.windowSize?.width || 0) / 2, x - state.step)
        ),
      moveWindowRight: () =>
        moveWindowHorizontal((x) =>
          Math.min(
            state.screenWidth - (state.windowSize?.width || 0) / 2,
            x + state.step
          )
        ),
      moveWindowUp: () => moveWindowVertical((y) => y - state.step),
      moveWindowDown: () => moveWindowVertical((y) => y + state.step)
    })
    await createWindow()
    state.shortcutsHelper?.registerGlobalShortcuts()

    // Initialize auto-updater regardless of environment
    initAutoUpdater()
    console.log(
      "Auto-updater initialized in",
      isDev ? "development" : "production",
      "mode"
    )
  } catch (error) {
    console.error("Failed to initialize application:", error)
    app.quit()
  }
}

// Handle the auth callback in development
app.on("open-url", (event, url) => {
  console.log("open-url event received:", url)
  event.preventDefault()
  if (url.startsWith("interview-coder://")) {
    handleAuthCallback(url, state.mainWindow)
  }
})

// Handle the auth callback in production (Windows/Linux)
app.on("second-instance", (_event, commandLine) => {
  console.log("second-instance event received:", commandLine)
  const url = commandLine.find((arg) => arg.startsWith("interview-coder://"))
  if (url) {
    handleAuthCallback(url, state.mainWindow)
  }

  // Focus or create the main window
  if (!state.mainWindow) {
    createWindow()
  } else {
    if (state.mainWindow.isMinimized()) state.mainWindow.restore()
    state.mainWindow.focus()
  }
})

// Prevent multiple instances of the app
if (!app.requestSingleInstanceLock()) {
  app.quit()
} else {
  app.on("window-all-closed", () => {
    // Clean up resources
    if (state.processingHelper) {
      state.processingHelper.cleanup();
    }

    if (process.platform !== "darwin") {
      app.quit()
      state.mainWindow = null
    }
  })

  // Also clean up when quitting
  app.on("before-quit", () => {
    console.log("Application is quitting, cleaning up resources...");
    if (state.processingHelper) {
      state.processingHelper.cleanup();
    }
  })
}

app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// State getter/setter functions
function getMainWindow(): BrowserWindow | null {
  return state.mainWindow
}

function getView(): "queue" | "solutions" | "debug" | "voice" {
  return state.view
}

function setView(view: "queue" | "solutions" | "debug" | "voice"): void {
  state.view = view
  state.screenshotHelper?.setView(view)
}

function getScreenshotHelper(): ScreenshotHelper | null {
  return state.screenshotHelper
}

function getProblemInfo(): any {
  return state.problemInfo
}

function setProblemInfo(problemInfo: any): void {
  state.problemInfo = problemInfo
}

function getScreenshotQueue(): string[] {
  return state.screenshotHelper?.getScreenshotQueue() || []
}

function getExtraScreenshotQueue(): string[] {
  return state.screenshotHelper?.getExtraScreenshotQueue() || []
}

function clearQueues(): void {
  state.screenshotHelper?.clearQueues()
  state.problemInfo = null
  setView("queue")
}

async function takeScreenshot(): Promise<string> {
  if (!state.mainWindow) throw new Error("No main window available")
  return (
    state.screenshotHelper?.takeScreenshot(
      () => hideMainWindow(),
      () => showMainWindow()
    ) || ""
  )
}

async function getImagePreview(filepath: string): Promise<string> {
  return state.screenshotHelper?.getImagePreview(filepath) || ""
}

async function deleteScreenshot(
  path: string
): Promise<{ success: boolean; error?: string }> {
  return (
    state.screenshotHelper?.deleteScreenshot(path) || {
      success: false,
      error: "Screenshot helper not initialized"
    }
  )
}

function setHasDebugged(value: boolean): void {
  state.hasDebugged = value
}

function getHasDebugged(): boolean {
  return state.hasDebugged
}

function getGeminiLiveService(): GeminiLiveService | null {
  return state.geminiLiveService
}

// Export state and functions for other modules
export {
  state,
  createWindow,
  hideMainWindow,
  showMainWindow,
  toggleMainWindow,
  setWindowDimensions,
  moveWindowHorizontal,
  moveWindowVertical,
  handleAuthCallback,
  getMainWindow,
  getView,
  setView,
  getScreenshotHelper,
  getProblemInfo,
  setProblemInfo,
  getScreenshotQueue,
  getExtraScreenshotQueue,
  clearQueues,
  takeScreenshot,
  getImagePreview,
  deleteScreenshot,
  setHasDebugged,
  getHasDebugged,
  getGeminiLiveService
}

// Clean up resources when app is quitting
app.on('before-quit', () => {
  console.log('Application is quitting, cleaning up resources...');

  // Clean up Gemini Live Service
  if (state.geminiLiveService) {
    console.log('Disposing Gemini Live Service');
    state.geminiLiveService.dispose();
    state.geminiLiveService = null;
  }

  // Clean up other resources as needed
  // Note: Add other cleanup code here if needed
});

app.whenReady().then(initializeApp)
