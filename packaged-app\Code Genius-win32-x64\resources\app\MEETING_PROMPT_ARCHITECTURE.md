# System Audio Processing Architecture in Interview Coder

## Overview
The Interview Coder application uses a sophisticated system audio processing architecture that captures interviewer speech from video calls, transcribes it using Deepgram, and generates contextual candidate responses using AI. This document focuses specifically on the system sound processing pipeline and excludes microphone/user voice functionality.

## Core Components

### 1. System Audio Capture Pipeline

#### Frontend System Audio Component (`src/_pages/Voice.tsx`)
The Voice component captures system audio from video calls, YouTube, and other applications:

```typescript
// System audio capture initialization
const initializeMediaRecorder = async () => {
  // Request system audio capture (screen audio)
  const stream = await navigator.mediaDevices.getDisplayMedia({
    audio: {
      echoCancellation: false,  // Preserve original audio quality
      noiseSuppression: false,  // Keep interviewer's natural speech
      autoGainControl: false,   // Maintain consistent volume levels
      sampleRate: 16000        // Optimal for speech recognition
    },
    video: false // Audio only for system capture
  });

  mediaRecorderRef.current = new MediaRecorder(stream, {
    mimeType: 'audio/webm;codecs=opus'
  });
};
```

#### System Audio Processing Flow
1. **System Audio Capture**: Uses `getDisplayMedia` API to capture system audio from video calls
2. **Continuous Monitoring**: Automatically detects when interviewer is speaking
3. **Buffer Creation**: Converts audio blob to Uint8Array buffer for processing
4. **IPC Communication**: Sends buffer to Electron main process with system audio flag

```typescript
// System audio processing in Voice.tsx (lines 890-891)
const result = await window.electronAPI.processVoiceInput(
  '', // Empty transcript to force Deepgram transcription
  currentLanguage,
  audioBuffer,
  false // isUserSpeech = false for system audio
);
```

### 2. System Audio IPC Handler (`electron/ipcHandlers.ts`)

The IPC handler processes system audio specifically for interviewer speech detection:

```typescript
// System Audio IPC Handler (lines 378-444)
ipcMain.handle("process-voice-input", async (
  event,
  transcript: string,
  language: string,
  audioBuffer?: Uint8Array,
  isUserSpeech: boolean = false, // Always false for system audio
  resumeInfo?: string,
  jobDescription?: string
) => {
  console.log('DEBUG: Processing system audio from video call');
  console.log('DEBUG: Audio source: System Audio (Interviewer)');

  // Process system audio with GeminiLiveService
  if (deps.geminiLiveService && deps.geminiLiveService.isReady()) {
    const result = await deps.geminiLiveService.processVoiceInput(
      transcript,
      language,
      audioBuffer ? Buffer.from(audioBuffer) : undefined,
      false, // isUserSpeech = false for system audio processing
      resumeInfo,
      jobDescription
    );
    return result;
  }

  // Fallback processing for system audio
  const response = await deps.processingHelper.processVoiceInput(transcript, language);
  return { success: true, response, transcript };
});
```

### 3. System Audio Speech-to-Text (`electron/DeepgramService.ts`)

#### Deepgram Integration for System Audio
The DeepgramService specifically handles system audio transcription from video calls:

```typescript
// System audio transcription optimized for interviewer speech (lines 746-758)
const { result, error } = await this.deepgram.listen.prerecorded.transcribeFile(
  audioData,
  {
    model: 'general',           // Optimized for interview conversations
    language: 'en',
    smart_format: true,         // Format for professional speech
    punctuate: true,           // Important for interview questions
    mimetype: mimeType,
    tier: 'base',
    profanity_filter: false,   // Preserve natural speech
    keywords: ['interview', 'job', 'experience', 'skills', 'project', 'education', 'background', 'position', 'company', 'team', 'work'],
    detect_language: true,     // Auto-detect interviewer's language
    diarize: false,           // Single speaker (interviewer) focus
    utterances: true          // Separate interview questions
  }
);
```

#### System Audio Format Optimization
The service handles system audio formats specifically:
- WebM from system audio capture (primary)
- WAV conversion for better compatibility
- Optimized for video call audio quality

### 4. AI Response Generation (`electron/GeminiLiveService.ts`)

#### Embedded Context Data
The service includes hardcoded resume and job description data:

```typescript
// Static context data (lines 34-188)
private static readonly DHANANJAY_RESUME: string = `
Remote | Email: <EMAIL> | Phone: +91-9123252856
PROFESSIONAL SUMMARY
Creative and performance-driven Marketing Associate with 3+ years of hands-on experience...
`;

private static readonly BEFOODIE_JOB_DESCRIPTION: string = `
Marketing Manager
Befoodie.Inc
Remote
₹18,000 - ₹25,000 a month
...
`;
```

#### System Audio Prompt Architecture
The system uses specialized prompts for processing interviewer speech from system audio:

**System Audio Prompt (lines 563-593):**
```typescript
// Specialized prompt for processing interviewer's speech from video calls
const prompt = `
You are my personal interview assistant, helping Dhananjay respond to an interviewer.
I am the interviewer, and you are guiding Dhananjay. Your task is to process what
the interviewer said and provide a suggested response that Dhananjay can use.

Here is the context to inform the suggested answer:

--- Dhananjay's Resume ---
${GeminiLiveService.DHANANJAY_RESUME}
--- End Resume ---

--- BeFoodie Inc. Job Description ---
${GeminiLiveService.BEFOODIE_JOB_DESCRIPTION}
--- End Job Description ---

Here's what Dhananjay heard from the interviewer: "${finalTranscript}"

Your task is to produce a response in two parts:
1. First, speaking *as the assistant* to Dhananjay, briefly rephrase the interviewer's
   question based on the transcript to confirm understanding. Start this part with
   "Okay, based on what was heard..." or "So, if I understand correctly, they're asking about..."

2. Second, provide a suggested answer speaking *as Dhananjay* directly to the interviewer.
   This suggested answer should be concise, confident, natural-sounding, and draw natural
   connections between Dhananjay's resume and the job description.

For the suggested answer (part 2):
- Use simple, clear English suitable for video call delivery
- Be specific, drawing connections between skills/experience and BeFoodie's needs
- Keep it brief (1-3 short paragraphs) for natural speech delivery
- Sound confident, enthusiastic, and professional
- Insert the placeholder \`[pause]\` after key phrases for natural speech flow
- Do NOT use meta-references like "As mentioned in my resume" in the suggested answer
- Clearly separate the two parts with "Suggested Answer:" before the second part
`;
```

### 5. System Audio Detection and Processing

#### System Audio Mode (`isUserSpeech: false`)
The system is specifically designed for system audio capture:

- **System Audio Capture**: Captures audio from video calls (Zoom, Teams, Google Meet, etc.)
- **Interviewer Speech Processing**: Processes interviewer's questions and statements
- **Contextual Response Generation**: Provides suggested responses for the candidate
- **Continuous Monitoring**: Automatically detects when interviewer is speaking

#### Auto-Detection Logic for System Audio
```typescript
// System audio detection optimized for video calls (lines 804-871)
public detectSystemAudio(audioBuffer: Buffer): boolean {
  if (!this.autoSystemSSTEnabled || !audioBuffer || audioBuffer.length < 100) {
    return false;
  }

  // Enhanced audio level detection with peak analysis for interview speech
  let sum = 0;
  let peakCount = 0;
  let maxConsecutivePeaks = 0;
  let currentConsecutivePeaks = 0;
  const sampleSize = Math.min(2000, audioBuffer.length);
  const step = Math.floor(audioBuffer.length / sampleSize);

  // Sample the buffer at regular intervals for speech detection
  for (let i = 0; i < sampleSize; i++) {
    const idx = i * step;
    if (idx < audioBuffer.length) {
      const sampleValue = Math.abs(audioBuffer[idx] - 128);
      sum += sampleValue;

      // Count peaks above threshold (indicating interviewer speech)
      if (sampleValue > 20) {
        peakCount++;
        currentConsecutivePeaks++;
        if (currentConsecutivePeaks > maxConsecutivePeaks) {
          maxConsecutivePeaks = currentConsecutivePeaks;
        }
      } else {
        currentConsecutivePeaks = 0;
      }
    }
  }

  const avgLevel = sum / sampleSize;
  const peakRatio = (peakCount / sampleSize) * 100;
  const threshold = Math.max(10, avgLevel * 1.5);

  // Multi-factor detection optimized for interview scenarios:
  // 1. Average level above threshold (interviewer speaking)
  // 2. Peak ratio above 15% (indicating speech patterns)
  // 3. Max consecutive peaks > 10 (indicating sustained interview questions)
  const hasInterviewerAudio = avgLevel > threshold || peakRatio > 15 || maxConsecutivePeaks > 10;

  if (hasInterviewerAudio) {
    this.lastSystemAudioTimestamp = Date.now();
    return true;
  } else {
    // Check if we've been silent for longer than threshold
    const silenceTime = Date.now() - this.lastSystemAudioTimestamp;
    return silenceTime < this.systemAudioSilenceThreshold;
  }
}
```

### 6. System Audio Response Processing

#### Two-Part Response System
The AI generates structured responses for system audio (interviewer speech):

```typescript
// Example system audio response format:
"Okay, based on what was heard, they're asking about your experience with performance marketing campaigns.

Suggested Answer:
I have over 3 years of hands-on experience in performance marketing [pause]
specifically with PPC, Meta Ads, and Google Ads campaigns [pause]
In my recent work, I achieved a 3.5x average ROAS for D2C brands [pause]
which directly aligns with BeFoodie's need for data-driven marketing growth."
```

#### Continuous System Audio Processing
The system supports continuous monitoring of video call audio:

```typescript
// Continuous system audio processing in Voice.tsx (lines 896-915)
if (isContinuousModeEnabled && response) {
  setResponse(prev => {
    const maxLength = 5000; // Limit response size for video call context
    let newResponse = prev;

    if (prev.length > maxLength) {
      newResponse = "...\n" + prev.substring(prev.length - maxLength);
    }

    // Append new interviewer question and suggested response
    return newResponse + "\n\n--- New Interview Question ---\n\n" + result.response;
  });
}
```

## System Audio Data Flow Summary

1. **System Audio Capture** → Voice.tsx captures video call audio via getDisplayMedia
2. **Audio Buffer Processing** → Converts system audio to Uint8Array and sends via IPC
3. **Interviewer Speech-to-Text** → DeepgramService transcribes interviewer questions
4. **Context Integration** → GeminiLiveService combines transcript with embedded candidate data
5. **AI Response Generation** → Gemini API generates two-part responses with pause markers
6. **Candidate Response Display** → Frontend displays suggested answers for video call delivery

## Key System Audio Features

- **System Audio Capture**: Dedicated video call audio processing from Zoom, Teams, Google Meet
- **Interviewer Speech Recognition**: Optimized Deepgram transcription for interview questions
- **Embedded Candidate Context**: Hardcoded resume and job description for consistent responses
- **Two-Part Response System**: Question clarification + suggested candidate answer
- **Continuous Interview Monitoring**: Real-time processing of interviewer speech
- **Video Call Optimization**: Audio settings optimized for video conferencing platforms
- **Natural Speech Delivery**: Pause placeholders for realistic candidate response delivery

This architecture enables seamless real-time interview assistance with context-aware AI responses tailored to the specific candidate profile and job requirements.

## Advanced Technical Implementation

### 7. Error Handling and Fallback Systems

#### Multi-Layer Fallback Architecture
The system implements comprehensive error handling with multiple fallback layers:

```typescript
// Primary: Deepgram + Gemini Live API
// Fallback 1: Browser Speech Recognition + Gemini Live API
// Fallback 2: Manual transcript input + Regular Gemini API
// Fallback 3: ProcessingHelper with basic prompts

// Error handling in ipcHandlers.ts (lines 511-548)
if (!deps.processingHelper) {
  deps.getMainWindow()?.webContents.send('voice-processing-error', {
    error: 'Voice processing service not initialized',
    details: 'Processing helper not available',
    transcript: transcript
  });
  return { success: false, error: "Processing helper not initialized" };
}
```

#### Deepgram Error Recovery
```typescript
// DeepgramService.ts fallback mechanism (lines 665-688)
if (isWebMFormat) {
  try {
    const wavBuffer = await this.convertWebmToWav(audioData);
    const { result, error } = await this.deepgram.listen.prerecorded.transcribeFile(
      wavBuffer,
      {
        model: 'general',
        language: 'en',
        smart_format: true,
        punctuate: true,
        mimetype: 'audio/wav',
        tier: 'base',
        keywords: ['interview', 'job', 'experience', 'skills']
      }
    );
  } catch (conversionError) {
    console.error('WebM to WAV conversion failed:', conversionError);
  }
}
```

### 8. Real-Time Audio Processing

#### System Audio Level Monitoring
The system continuously monitors system audio levels for intelligent interviewer speech detection:

```typescript
// Voice.tsx system audio level detection (lines 600-650)
const analyzeSystemAudio = () => {
  if (analyzerRef.current && dataArrayRef.current && isCapturingSystemAudio) {
    analyzerRef.current.getByteFrequencyData(dataArrayRef.current);

    let sum = 0;
    for (let i = 0; i < dataArrayRef.current.length; i++) {
      sum += dataArrayRef.current[i];
    }

    const average = sum / dataArrayRef.current.length;
    setAudioLevel(average);

    // Auto-detection logic for interviewer speech
    if (isAutoDetectEnabled && !isListening && average > audioThreshold) {
      console.log('Auto-starting system audio recording - interviewer detected');
      startRecording(true); // Always true for system audio mode
    }
  }
};
```

#### Continuous System Audio Processing Mode
```typescript
// Continuous system audio processing configuration (lines 813-829)
const minSize = isContinuousModeEnabled ? 500 : 1000;  // Smaller chunks for real-time interview processing
const minBufferSize = isContinuousModeEnabled ? 2500 : 5000;

// Process smaller system audio chunks in continuous mode for real-time interview assistance
if (audioBlob.size < minSize) {
  if (isContinuousModeEnabled) {
    console.log('In continuous system audio mode - skipping small chunk but continuing to monitor interviewer');
  } else {
    showToast("Warning", "System audio recording too short, continuing to monitor", "neutral");
  }
}

// Ensure we're always in system audio mode for video calls
const isSystemAudioMode = true; // Always true for interviewer speech processing
const isUserSpeech = false;     // Always false for system audio processing
```

### 9. Context Management and Data Persistence

#### Resume and Job Description Integration
```typescript
// Voice.tsx context handling (lines 1182-1216)
const handleSaveResumeJob = (newResumeInfo: string, newJobDescription: string) => {
  setResumeInfo(newResumeInfo);
  setJobDescription(newJobDescription);

  // Store in localStorage for persistence
  localStorage.setItem('resumeInfo', newResumeInfo);
  localStorage.setItem('jobDescription', newJobDescription);

  // Send to main process via IPC
  if (window.electron && window.electron.ipcRenderer) {
    window.electron.ipcRenderer.send('update-resume-job-info', newResumeInfo, newJobDescription);
  }

  // Custom event for additional handling
  const customEvent = new CustomEvent('resume-job-info-updated', {
    detail: { resumeInfo: newResumeInfo, jobDescription: newJobDescription }
  });
  window.dispatchEvent(customEvent);
};
```

#### System Audio Data Persistence Strategy
```typescript
// localStorage integration for system audio settings (lines 1073-1108)
useEffect(() => {
  // Load saved system audio settings on component mount
  const savedSystemAudioTranscript = localStorage.getItem('savedSystemAudioTranscript');
  const savedResumeInfo = localStorage.getItem('resumeInfo');
  const savedJobDescription = localStorage.getItem('jobDescription');
  const savedSystemAudioAutoDetect = localStorage.getItem('systemAudioAutoDetectEnabled');
  const savedContinuousSystemAudioMode = localStorage.getItem('continuousSystemAudioModeEnabled');

  // Apply saved system audio settings
  if (savedSystemAudioTranscript) setTranscript(savedSystemAudioTranscript);
  if (savedResumeInfo) setResumeInfo(savedResumeInfo);
  if (savedJobDescription) setJobDescription(savedJobDescription);
  if (savedSystemAudioAutoDetect !== null) setIsAutoDetectEnabled(savedSystemAudioAutoDetect === 'true');
  if (savedContinuousSystemAudioMode !== null) setIsContinuousModeEnabled(savedContinuousSystemAudioMode === 'true');

  // Always enable system audio capture for video calls
  setIsCapturingSystemAudio(true);
}, []);
```

### 10. AI Model Configuration and Safety

#### Gemini API Configuration
```typescript
// GeminiLiveService.ts model configuration (lines 621-647)
const generationConfig = {
  temperature: 0.6,        // Balanced creativity vs consistency
  topK: 20,               // Consider top 20 probability tokens
  topP: 0.9,              // Cumulative probability threshold
  maxOutputTokens: 600,   // Response length limit
};

const safetySettings = [
  {
    category: "HARM_CATEGORY_HARASSMENT",
    threshold: "BLOCK_MEDIUM_AND_ABOVE",
  },
  {
    category: "HARM_CATEGORY_HATE_SPEECH",
    threshold: "BLOCK_MEDIUM_AND_ABOVE",
  },
  {
    category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
    threshold: "BLOCK_MEDIUM_AND_ABOVE",
  },
  {
    category: "HARM_CATEGORY_DANGEROUS_CONTENT",
    threshold: "BLOCK_MEDIUM_AND_ABOVE",
  },
];
```

#### API Key Management
```typescript
// Multi-tier API key system (lines 260-269)
private getEffectiveApiKey(): string {
  // Prioritize direct apiKey property
  if (this.apiKey && this.apiKey.length > 0) {
    return this.apiKey;
  } else if (this.fallbackApiKey && this.fallbackApiKey.length > 0) {
    console.log('DEBUG: Using fallback API key for Gemini Live');
    return this.fallbackApiKey;
  }
  return '';
}
```

### 11. Performance Optimization

#### System Audio Memory Management
```typescript
// Response size limiting for continuous system audio processing (lines 900-910)
if (isContinuousModeEnabled && response) {
  setResponse(prev => {
    const maxLength = 5000; // Maximum characters for video call context
    let newResponse = prev;

    if (prev.length > maxLength) {
      // Keep only the last part of the previous interview responses
      newResponse = "...\n" + prev.substring(prev.length - maxLength);
    }

    // Append new interviewer question and suggested candidate response
    return newResponse + "\n\n--- New Interview Question ---\n\n" + result.response;
  });
}
```

#### System Audio Resource Cleanup
```typescript
// Voice.tsx system audio cleanup (lines 1047-1070)
return () => {
  // Stop system audio recording if active
  if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
    console.log('Stopping system audio recording');
    mediaRecorderRef.current.stop();
  }

  // Clean up system audio analyzer
  if (audioContextRef.current) {
    console.log('Closing system audio context');
    audioContextRef.current.close();
  }

  // Clear system audio detection timeouts
  if (audioDetectionTimeoutRef.current) {
    clearTimeout(audioDetectionTimeoutRef.current);
  }

  // Stop system audio stream
  if (systemAudioStreamRef.current) {
    systemAudioStreamRef.current.getTracks().forEach(track => track.stop());
  }
};
```

### 12. Event-Driven Architecture

#### System Audio IPC Event System
```typescript
// Main process system audio event handling
deps.getMainWindow()?.webContents.send('system-audio-processing-error', {
  error: 'System audio processing failed',
  details: error.message,
  stack: error.stack,
  transcript: transcript,
  source: 'system_audio'
});

deps.getMainWindow()?.webContents.send('interviewer-response-ready', {
  text: text,
  transcript: finalTranscript,
  responseType: 'two_part_suggestion',
  source: 'system_audio'
});

deps.getMainWindow()?.webContents.send('interviewer-speech-transcription', {
  transcript: finalTranscript,
  isUser: false, // Always false for system audio
  source: 'video_call'
});
```

#### Frontend System Audio Event Listeners
```typescript
// Voice.tsx system audio event handling for real-time interview assistance
useEffect(() => {
  // Listen for interviewer speech transcription results
  window.electronAPI?.onSpeechTranscription?.((data) => {
    if (!data.isUser && data.source === 'video_call') {
      setTranscript(data.transcript);
      console.log('Interviewer said:', data.transcript);
    }
  });

  // Listen for system audio processing errors
  window.electronAPI?.onVoiceProcessingError?.((error) => {
    if (error.source === 'system_audio') {
      showToast("System Audio Error", error.error, "error");
    }
  });

  // Listen for interviewer response suggestions
  window.electronAPI?.onInterviewerResponseReady?.((response) => {
    if (response.responseType === 'two_part_suggestion') {
      setResponse(response.text);
      console.log('Suggested response ready for interviewer question');
    }
  });
}, []);
```

## Architecture Benefits

1. **Modularity**: Clear separation between audio capture, transcription, and AI processing
2. **Resilience**: Multiple fallback mechanisms ensure system reliability
3. **Context Awareness**: Embedded candidate data provides consistent, relevant responses
4. **Real-Time Processing**: Continuous audio monitoring and processing capabilities
5. **Performance**: Memory management and resource cleanup prevent performance degradation
6. **Flexibility**: Dual audio source support for different interview scenarios
7. **User Experience**: Intelligent auto-detection and seamless mode switching

This comprehensive architecture ensures robust, real-time interview assistance with intelligent context management and reliable fallback systems.
