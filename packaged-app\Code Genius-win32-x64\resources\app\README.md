# Code Genius - AI-Powered Interview Assistant

An advanced AI-powered coding assistant designed specifically for technical interviews and coding challenges. This Electron-based application combines voice interaction, real-time speech-to-text, and intelligent AI responses to help you excel in coding interviews.

## 🚀 Features

### Core Capabilities
- **AI-Powered Interview Assistant**: Leverages Google's Gemini AI for intelligent responses
- **Voice Interaction**: Real-time speech-to-text using Deepgram API
- **Multi-Modal Input**: Support for both voice and text input
- **Live Audio Processing**: Real-time system audio capture and processing
- **Smart Context Awareness**: Maintains conversation context for better responses
- **Cross-Platform**: Available on Windows, macOS, and Linux

### Interview-Specific Features
- **Technical Question Assistance**: Get help with coding problems and algorithms
- **Code Explanation**: Detailed explanations of complex code snippets
- **Problem-Solving Guidance**: Step-by-step approach to technical challenges
- **Real-time Feedback**: Instant responses to your questions
- **Multi-Language Support**: Support for various programming languages

### Advanced Features
- **Autonomous Mode**: Ultra-sensitive audio detection for continuous operation
- **System Audio Monitoring**: Capture and process interviewer's questions automatically
- **Pause-Aware Responses**: Natural speech patterns with strategic pauses
- **Context Preservation**: Maintains interview context throughout the session
- **Fallback Mechanisms**: Multiple speech recognition options for reliability

## 📋 System Requirements

### Minimum Requirements
- **Operating System**: Windows 10/11, macOS 10.14+, or Linux (Ubuntu 18.04+)
- **RAM**: 4GB minimum, 8GB+ recommended
- **Storage**: 1GB free space
- **Internet Connection**: Required for AI and speech services
- **Microphone**: Required for voice input
- **Audio Output**: Required for system audio monitoring

### Recommended Specifications
- **RAM**: 16GB for optimal performance
- **CPU**: Multi-core processor (Intel i5/AMD Ryzen 5 or better)
- **Network**: Stable broadband connection (10+ Mbps)

## 🛠️ Prerequisites

Ensure you have the following installed:
- **Node.js** (v18.0.0 or higher) - [Download here](https://nodejs.org/)
- **npm** (comes with Node.js) or **yarn**
- **Git** - [Download here](https://git-scm.com/)
- **Google Gemini API key** - [Get one here](https://makersuite.google.com/app/apikey)
- **Deepgram API key** (optional but recommended) - [Get one here](https://console.deepgram.com/)

### Permissions Required
- **Microphone Access**: Required for voice input
- **Screen Recording Permission** (macOS):
  1. Go to System Preferences > Security & Privacy > Privacy > Screen Recording
  2. Ensure that Code Genius has screen recording permission enabled
  3. Restart the application after enabling permissions
- **Windows**: No additional permissions needed
- **Linux**: May require `xhost` access depending on your distribution

## 🛠️ Installation

### Step 1: Clone the Repository
```bash
git clone https://github.com/yourusername/code-genius.git
cd code-genius
```

### Step 2: Install Dependencies
```bash
# Using npm
npm install

# Or using yarn
yarn install
```

### Step 3: Environment Configuration
Create a `.env` file in the root directory with the following configuration:

```env
# Gemini API Keys
# Get your API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY="your_gemini_api_key_here"
GEMINI_LIVE_API_KEY="your_gemini_api_key_here"

# Deepgram API Key for speech-to-text
# Get your API key from: https://console.deepgram.com/
DEEPGRAM_API_KEY="your_deepgram_api_key_here"

# Supabase Configuration (Optional - for user management)
VITE_SUPABASE_URL="https://your-project.supabase.co"
VITE_SUPABASE_ANON_KEY="your_supabase_anon_key"

# Environment
NODE_ENV=development
```

### Step 4: API Key Setup

#### Gemini API Key
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Create a new API key
4. Copy the key and add it to your `.env` file

#### Deepgram API Key (Optional but Recommended)
1. Visit [Deepgram Console](https://console.deepgram.com/)
2. Create a free account
3. Generate an API key
4. Copy the key and add it to your `.env` file

> **Note**: Deepgram provides superior speech-to-text capabilities. Without it, the app will fall back to browser-based speech recognition.

## 🚀 Running the Application

### Development Mode
```bash
npm run dev
```

This command will:
- Clean previous builds
- Start TypeScript compilation in watch mode
- Launch the Vite development server on `http://localhost:54321`
- Open the Electron application
- Enable hot-reloading for development

### Production Build
```bash
npm run build
```

This will create:
- Optimized React build in `dist/` directory
- Compiled Electron files in `dist-electron/` directory
- Platform-specific executables in `release/` directory

## 🎯 Usage Guide

### First Launch
1. Start the application using `npm run dev`
2. The app will automatically validate your API keys
3. Grant microphone permissions when prompted
4. Configure audio settings in the settings panel

### Basic Operation
1. **Voice Input**: Click the microphone button and speak your question
2. **Text Input**: Type your question in the chat interface
3. **System Audio**: Enable autonomous mode to capture interviewer questions
4. **Context Management**: The app maintains conversation context automatically

### Interview Mode
1. Enable "Autonomous Mode" for continuous operation
2. The app will automatically detect and process interviewer questions
3. Responses include natural pauses for realistic speech patterns
4. Use the built-in ISRO Hackathon context for technical discussions

### Advanced Features
- **Audio Sensitivity**: Adjust detection thresholds in settings
- **Response Customization**: Modify AI prompts for specific interview types
- **Fallback Options**: Multiple speech recognition methods available
- **Debug Mode**: Enable detailed logging for troubleshooting

## 🏗️ Technology Stack

- **Frontend**: React 18 + TypeScript + Vite
- **Backend**: Electron (Node.js)
- **UI Framework**: Tailwind CSS + Radix UI
- **AI Integration**: Google Gemini API
- **Speech-to-Text**: Deepgram API + Browser Speech Recognition
- **Audio Processing**: Custom audio conversion and detection
- **State Management**: React Query + Context API

## 🔧 Configuration

### Audio Settings
- **Microphone Access**: Grant microphone permissions for voice input
- **System Audio**: Enable system audio capture for autonomous mode
- **Audio Sensitivity**: Adjust detection thresholds in settings
- **Noise Filtering**: Configure background noise suppression

### AI Configuration
- **Model Selection**: Choose between different Gemini models
- **Response Style**: Customize AI response patterns
- **Context Length**: Adjust conversation memory length
- **Safety Settings**: Configure content filtering levels

## 🐛 Troubleshooting

### Common Issues

#### API Key Issues
- Verify your Gemini API key is valid and has sufficient quota
- Check that the API key has the necessary permissions
- Ensure the key is correctly formatted in the `.env` file

#### Audio Problems
- Grant microphone permissions in your operating system
- Check audio device settings in the application
- Verify Deepgram API key if using advanced speech recognition
- Test with different audio input devices

#### Build Issues
- Clear `node_modules` and reinstall dependencies
- Ensure Node.js version is 18.0.0 or higher
- Check for TypeScript compilation errors
- Verify all required environment variables are set

### Debug Mode
Enable debug logging by setting:
```env
NODE_ENV=development
OPEN_DEV_TOOLS=true
```

## 📝 Contributing

We welcome contributions! Please follow these steps:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Use ESLint and Prettier for code formatting
- Write comprehensive tests for new features
- Update documentation for API changes

## 📄 License

This project is licensed under the ISC License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Google Gemini AI for powerful language processing
- Deepgram for advanced speech-to-text capabilities
- Electron team for the cross-platform framework
- React and TypeScript communities for excellent tooling

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Check the troubleshooting section above
- Review the configuration documentation

---

**Note**: This application is designed for educational and interview preparation purposes. Please ensure compliance with your organization's policies regarding AI assistance tools.
