// create-sample-audio.js
// This script creates a simple WAV file that can be used to test Deepgram transcription

const fs = require('fs');
const path = require('path');

// Function to create a simple WAV file with a sine wave tone
function createWavFile(filePath, durationSec = 3, frequency = 440) {
  const sampleRate = 44100;
  const numSamples = durationSec * sampleRate;
  const amplitude = 0.5; // Volume (0-1)
  
  // Create the WAV header
  const headerSize = 44;
  const dataSize = numSamples * 2; // 16-bit samples (2 bytes per sample)
  const fileSize = headerSize + dataSize;
  
  const buffer = Buffer.alloc(fileSize);
  
  // RIFF header
  buffer.write('RIFF', 0);
  buffer.writeUInt32LE(fileSize - 8, 4); // File size - 8
  buffer.write('WAVE', 8);
  
  // Format chunk
  buffer.write('fmt ', 12);
  buffer.writeUInt32LE(16, 16); // Format chunk size
  buffer.writeUInt16LE(1, 20); // Audio format (1 = PCM)
  buffer.writeUInt16LE(1, 22); // Number of channels
  buffer.writeUInt32LE(sampleRate, 24); // Sample rate
  buffer.writeUInt32LE(sampleRate * 2, 28); // Byte rate (SampleRate * NumChannels * BitsPerSample/8)
  buffer.writeUInt16LE(2, 32); // Block align (NumChannels * BitsPerSample/8)
  buffer.writeUInt16LE(16, 34); // Bits per sample
  
  // Data chunk
  buffer.write('data', 36);
  buffer.writeUInt32LE(dataSize, 40); // Data chunk size
  
  // Write the audio data (sine wave)
  for (let i = 0; i < numSamples; i++) {
    const t = i / sampleRate;
    const value = Math.sin(2 * Math.PI * frequency * t) * amplitude;
    const sample = Math.floor(value * 32767); // Convert to 16-bit
    buffer.writeInt16LE(sample, headerSize + i * 2);
  }
  
  // Write the file
  fs.writeFileSync(filePath, buffer);
  console.log(`Created WAV file: ${filePath}`);
}

// Create a sample WAV file
const sampleWavPath = path.join(__dirname, 'sample-audio.wav');
createWavFile(sampleWavPath);

console.log('Sample audio file created successfully.');
