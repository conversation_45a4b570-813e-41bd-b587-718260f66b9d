import { spawn } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

export class AudioConverter {
  private tempDir: string;

  constructor() {
    // Create a temporary directory for audio conversion
    this.tempDir = path.join(os.tmpdir(), 'audio-converter');
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }
  }

  /**
   * Convert WebM audio buffer to WAV format using FFmpeg
   * @param webmBuffer - Input WebM audio buffer
   * @returns Promise<Buffer> - Converted WAV audio buffer
   */
  async convertWebMToWAV(webmBuffer: Buffer): Promise<Buffer> {
    const inputFile = path.join(this.tempDir, `input_${Date.now()}.webm`);
    const outputFile = path.join(this.tempDir, `output_${Date.now()}.wav`);

    try {
      // Write WebM buffer to temporary file
      fs.writeFileSync(inputFile, webmBuffer);

      console.log('AudioConverter: Converting WebM to WAV...');
      console.log('AudioConverter: Input file size:', webmBuffer.length, 'bytes');

      // Convert using FFmpeg
      await this.runFFmpeg(inputFile, outputFile);

      // Read the converted WAV file
      const wavBuffer = fs.readFileSync(outputFile);
      console.log('AudioConverter: Conversion successful, output size:', wavBuffer.length, 'bytes');

      return wavBuffer;
    } catch (error) {
      console.error('AudioConverter: Conversion failed:', error);
      throw error;
    } finally {
      // Clean up temporary files
      this.cleanupFile(inputFile);
      this.cleanupFile(outputFile);
    }
  }

  /**
   * Run FFmpeg conversion command
   * @param inputFile - Input file path
   * @param outputFile - Output file path
   */
  private runFFmpeg(inputFile: string, outputFile: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const ffmpegArgs = [
        '-i', inputFile,           // Input file
        '-acodec', 'pcm_s16le',    // PCM 16-bit little-endian codec
        '-ar', '16000',            // Sample rate 16kHz (good for speech recognition)
        '-ac', '1',                // Mono channel
        '-f', 'wav',               // WAV format
        '-y',                      // Overwrite output file
        outputFile
      ];

      console.log('AudioConverter: Running FFmpeg with args:', ffmpegArgs.join(' '));

      const ffmpeg = spawn('ffmpeg', ffmpegArgs);

      let stderr = '';

      ffmpeg.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      ffmpeg.on('close', (code) => {
        if (code === 0) {
          console.log('AudioConverter: FFmpeg conversion completed successfully');
          resolve();
        } else {
          console.error('AudioConverter: FFmpeg failed with code:', code);
          console.error('AudioConverter: FFmpeg stderr:', stderr);
          reject(new Error(`FFmpeg failed with code ${code}: ${stderr}`));
        }
      });

      ffmpeg.on('error', (error) => {
        console.error('AudioConverter: FFmpeg spawn error:', error);
        reject(error);
      });
    });
  }

  /**
   * Detect audio format from buffer header
   * @param buffer - Audio buffer
   * @returns string - Detected format
   */
  detectAudioFormat(buffer: Buffer): string {
    if (buffer.length < 20) {
      return 'unknown';
    }

    // Check for WebM signature
    const webmSignature = Buffer.from([0x1A, 0x45, 0xDF, 0xA3]);
    if (buffer.subarray(0, 4).equals(webmSignature)) {
      return 'webm';
    }

    // Check for WAV signature
    if (buffer.subarray(0, 4).toString() === 'RIFF' && 
        buffer.subarray(8, 12).toString() === 'WAVE') {
      return 'wav';
    }

    // Check for OGG signature
    if (buffer.subarray(0, 4).toString() === 'OggS') {
      return 'ogg';
    }

    // Check for MP3 signature
    if (buffer[0] === 0xFF && (buffer[1] & 0xE0) === 0xE0) {
      return 'mp3';
    }

    return 'unknown';
  }

  /**
   * Clean up temporary file
   * @param filePath - File path to clean up
   */
  private cleanupFile(filePath: string): void {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log('AudioConverter: Cleaned up temporary file:', filePath);
      }
    } catch (error) {
      console.warn('AudioConverter: Failed to clean up file:', filePath, error);
    }
  }

  /**
   * Clean up all temporary files in the temp directory
   */
  cleanup(): void {
    try {
      if (fs.existsSync(this.tempDir)) {
        const files = fs.readdirSync(this.tempDir);
        for (const file of files) {
          this.cleanupFile(path.join(this.tempDir, file));
        }
      }
    } catch (error) {
      console.warn('AudioConverter: Failed to cleanup temp directory:', error);
    }
  }
}
