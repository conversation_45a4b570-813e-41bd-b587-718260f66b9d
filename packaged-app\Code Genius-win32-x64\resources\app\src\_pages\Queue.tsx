import React, { useState, useEffect, useRef } from "react"
import { useQuery } from "@tanstack/react-query"
import ScreenshotQueue from "../components/Queue/ScreenshotQueue"
import QueueCommands from "../components/Queue/QueueCommands"

import { useToast } from "../contexts/toast"
import { Screenshot } from "../types/screenshots"
import { COMMAND_KEY } from "../utils/platform"

async function fetchScreenshots(): Promise<Screenshot[]> {
  try {
    const response = await window.electronAPI.getScreenshots()
    if (response.success && response.previews) {
      return response.previews
    }
    return []
  } catch (error) {
    console.error("Error loading screenshots:", error)
    return []
  }
}

interface QueueProps {
  setView: (view: "queue" | "solutions" | "debug") => void
  credits: number
  currentLanguage: string
  setLanguage: (language: string) => void
}

const Queue: React.FC<QueueProps> = ({
  setView,
  credits,
  currentLanguage,
  setLanguage
}) => {
  const { showToast } = useToast()

  const [isTooltipVisible, setIsTooltipVisible] = useState(false)
  const [tooltipHeight, setTooltipHeight] = useState(0)
  const contentRef = useRef<HTMLDivElement>(null)

  const {
    data: screenshots = [],
    // isLoading is used in the ScreenshotQueue component
    refetch
  } = useQuery<Screenshot[]>({
    queryKey: ["screenshots"],
    queryFn: fetchScreenshots,
    staleTime: Infinity,
    gcTime: Infinity,
    refetchOnWindowFocus: false
  })

  const handleDeleteScreenshot = async (index: number) => {
    const screenshotToDelete = screenshots[index]

    try {
      const response = await window.electronAPI.deleteScreenshot(
        screenshotToDelete.path
      )

      if (response.success) {
        refetch() // Refetch screenshots instead of managing state directly
      } else {
        console.error("Failed to delete screenshot:", response.error)
        showToast("Error", "Failed to delete the screenshot file", "error")
      }
    } catch (error) {
      console.error("Error deleting screenshot:", error)
    }
  }

  useEffect(() => {
    // Height update logic
    const updateDimensions = () => {
      if (contentRef.current) {
        let contentHeight = contentRef.current.scrollHeight
        const contentWidth = contentRef.current.scrollWidth
        if (isTooltipVisible) {
          contentHeight += tooltipHeight
        }
        window.electronAPI.updateContentDimensions({
          width: contentWidth,
          height: contentHeight
        })
      }
    }

    // Initialize resize observer
    const resizeObserver = new ResizeObserver(updateDimensions)
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current)
    }
    updateDimensions()

    // Set up event listeners
    const cleanupFunctions = [
      window.electronAPI.onScreenshotTaken(() => refetch()),
      window.electronAPI.onResetView(() => refetch()),

      window.electronAPI.onSolutionError((error: string) => {
        showToast(
          "Processing Failed",
          "There was an error processing your screenshots.",
          "error"
        )
        setView("queue") // Revert to queue if processing fails
        console.error("Processing error:", error)
      }),
      window.electronAPI.onProcessingNoScreenshots(() => {
        showToast(
          "No Screenshots",
          "There are no screenshots to process.",
          "neutral"
        )
      }),
      window.electronAPI.onOutOfCredits(() => {
        showToast(
          "Out of Credits",
          "You are out of credits. Please refill at https://www.interviewcoder.co/settings.",
          "error"
        )
      })
    ]

    return () => {
      resizeObserver.disconnect()
      cleanupFunctions.forEach((cleanup) => cleanup())
    }
  }, [isTooltipVisible, tooltipHeight])

  const handleTooltipVisibilityChange = (visible: boolean, height: number) => {
    setIsTooltipVisible(visible)
    setTooltipHeight(height)
  }

  return (
    <div ref={contentRef} className="bg-gray-900 w-full max-w-4xl mx-auto">
      <div className="px-6 py-5">
        <div className="space-y-6">
          <div className="bg-gradient-to-r from-blue-600/10 to-purple-600/10 rounded-lg p-5 border border-blue-500/20">
            <div className="flex items-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <circle cx="8.5" cy="8.5" r="1.5"></circle>
                <polyline points="21 15 16 10 5 21"></polyline>
              </svg>
              <h2 className="text-lg font-semibold text-white">Screenshot Queue</h2>

              <div className="ml-auto flex items-center space-x-2">
                <span className="text-sm text-gray-400">{screenshots.length} screenshots</span>
                <div className={`w-2 h-2 rounded-full ${screenshots.length > 0 ? 'bg-green-500' : 'bg-gray-500'}`}></div>
              </div>
            </div>

            <ScreenshotQueue
              isLoading={false}
              screenshots={screenshots}
              onDeleteScreenshot={handleDeleteScreenshot}
            />
          </div>

          <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-lg p-5 border border-gray-700">
            <div className="flex items-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-400 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
              </svg>
              <h2 className="text-lg font-semibold text-white">Actions</h2>
            </div>

            <QueueCommands
              onTooltipVisibilityChange={handleTooltipVisibilityChange}
              screenshotCount={screenshots.length}
              credits={credits}
              currentLanguage={currentLanguage}
              setLanguage={setLanguage}
            />
          </div>

          {/* Quick Help Section */}
          <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                <line x1="12" y1="17" x2="12.01" y2="17"></line>
              </svg>
              <h3 className="text-sm font-medium text-gray-300">Quick Help</h3>
            </div>
            <ul className="text-xs text-gray-400 space-y-1 ml-6 list-disc">
              <li>Press <kbd className="px-1.5 py-0.5 bg-gray-700 border border-gray-600 rounded text-gray-300 text-xs">{COMMAND_KEY}+B</kbd> to toggle the window visibility</li>
              <li>Press <kbd className="px-1.5 py-0.5 bg-gray-700 border border-gray-600 rounded text-gray-300 text-xs">{COMMAND_KEY}+H</kbd> to take a screenshot</li>
              <li>Press <kbd className="px-1.5 py-0.5 bg-gray-700 border border-gray-600 rounded text-gray-300 text-xs">{COMMAND_KEY}+Enter</kbd> to process screenshots</li>
              <li>Use <kbd className="px-1.5 py-0.5 bg-gray-700 border border-gray-600 rounded text-gray-300 text-xs">{COMMAND_KEY}+Arrow</kbd> keys to move the window</li>
              <li>Select your preferred programming language from the dropdown in the header</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Queue
