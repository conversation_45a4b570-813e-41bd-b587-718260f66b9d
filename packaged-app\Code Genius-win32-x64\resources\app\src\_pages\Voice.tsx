import React, { useState, useEffect, useRef } from "react";
import { useToast } from "../contexts/toast";
import { COMMAND_KEY } from "../utils/platform";
import ResumeJobInput from "../components/ResumeJobInput";

// Add TypeScript declarations for the Web Speech API
interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  start(): void;
  stop(): void;
  onresult: (event: any) => void;
  onerror: (event: any) => void;
  onend: () => void;
}

declare global {
  interface Window {
    SpeechRecognition: new () => SpeechRecognition;
    webkitSpeechRecognition: new () => SpeechRecognition;
  }
}

interface VoiceProps {
  setView: (view: "queue" | "solutions" | "debug" | "voice") => void;
  credits: number;
  currentLanguage: string;
  setLanguage: (language: string) => void;
}

const Voice: React.FC<VoiceProps> = ({
  // We're using these props but TypeScript doesn't detect it
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  setView,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  credits,
  currentLanguage,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  setLanguage
}) => {
  const { showToast: originalShowToast } = useToast();
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState("");
  const [response, setResponse] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [isUsingDeepgram, setIsUsingDeepgram] = useState(true);
  const [isCapturingSystemAudio, setIsCapturingSystemAudio] = useState(false);
  const [isSystemAudioMode, setIsSystemAudioMode] = useState(false);
  const [resumeInfo, setResumeInfo] = useState('');
  const [jobDescription, setJobDescription] = useState('');
  const [isAutoDetectEnabled, setIsAutoDetectEnabled] = useState(true); // Auto-detection enabled by default
  const [isContinuousModeEnabled, setIsContinuousModeEnabled] = useState(true); // Continuous processing mode - ENABLED BY DEFAULT
  const [isSystemAudioDetected, setIsSystemAudioDetected] = useState(false); // Flag for when system audio is detected
  const [audioLevel, setAudioLevel] = useState(0); // Current audio level for visualization
  const [isAutonomousMode, setIsAutonomousMode] = useState(true); // Fully autonomous operation
  const [autoStartSystemAudio, setAutoStartSystemAudio] = useState(true); // Auto-start system audio capture

  // Toast debounce mechanism
  const lastToastRef = useRef<{title: string, time: number}>({ title: '', time: 0 });
  const categoryDebounceRef = useRef<{[key: string]: number}>({
    'auto-detect': 0,
    'error': 0,
    'system-audio': 0,
    'recording': 0
  });

  // Helper to categorize toasts
  const getToastCategory = (title: string): string => {
    const lowerTitle = title.toLowerCase();
    if (lowerTitle.includes('auto-detect') || lowerTitle.includes('audio detected')) return 'auto-detect';
    if (lowerTitle.includes('error') || lowerTitle.includes('failed') || lowerTitle.includes('warning')) return 'error';
    if (lowerTitle.includes('system audio')) return 'system-audio';
    if (lowerTitle.includes('recording')) return 'recording';
    return 'other';
  };

  // Debounced toast function to prevent multiple similar toasts
  const showToast = (title: string, description: string, variant: "neutral" | "success" | "error") => {
    const now = Date.now();
    const lastToast = lastToastRef.current;
    const category = getToastCategory(title);
    const lastCategoryTime = categoryDebounceRef.current[category] || 0;

    // Don't show duplicate toasts within 3 seconds
    if (lastToast.title === title && now - lastToast.time < 3000) {
      console.log(`Suppressing duplicate toast: ${title}`);
      return;
    }

    // Don't show toasts from the same category too frequently (except errors)
    const categoryDebounceTime = category === 'error' ? 1000 : 5000; // Shorter for errors
    if (category !== 'other' && now - lastCategoryTime < categoryDebounceTime) {
      console.log(`Suppressing toast from category ${category}: ${title}`);
      return;
    }

    // Update last toast reference
    lastToastRef.current = { title, time: now };
    categoryDebounceRef.current[category] = now;

    // Show the toast
    originalShowToast(title, description, variant);
    console.log(`Showing toast: ${title} (category: ${category})`);
  };
  const contentRef = useRef<HTMLDivElement>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const systemAudioStreamRef = useRef<MediaStream | null>(null);
  const combinedStreamRef = useRef<MediaStream | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const audioDetectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Reference to the SpeechRecognition API (browser fallback)
  const recognitionRef = useRef<any>(null);

  // Listen for transcription events from the main process
  useEffect(() => {
    const handleTranscription = (_event: any, data: { transcript: string }) => {
      console.log('Received transcription from Deepgram:', data.transcript);
      setTranscript(data.transcript);
    };

    const handleTranscriptionError = (_event: any, data: { error: string }) => {
      console.error('Transcription error:', data.error);
      showToast("Error", `Speech recognition error: ${data.error}`, "error");
      setIsListening(false);
    };

    const handleGeminiResponse = (_event: any, data: { text: string, transcript: string }) => {
      console.log('Received response from Gemini API');
      setResponse(data.text);
      setIsProcessing(false);
    };

    const handleGeminiError = (_event: any, data: { error: string }) => {
      console.error('Gemini API error:', data.error);
      showToast("Error", `AI processing error: ${data.error}`, "error");
      setIsProcessing(false);
    };

    // Handle fallback to browser speech recognition
    const handleUseBrowserSpeechRecognition = (_event: any, data: { message: string }) => {
      console.log('Received fallback to browser speech recognition:', data.message);
      showToast("Info", "Falling back to browser speech recognition", "neutral");
      setIsUsingDeepgram(false);
      initializeBrowserSpeechRecognition();
    };

    // Add event listeners
    window.electron?.ipcRenderer.on('speech-transcription', handleTranscription);
    window.electron?.ipcRenderer.on('speech-transcription-error', handleTranscriptionError);
    window.electron?.ipcRenderer.on('gemini-live-response', handleGeminiResponse);
    window.electron?.ipcRenderer.on('gemini-live-error', handleGeminiError);
    window.electron?.ipcRenderer.on('use-browser-speech-recognition', handleUseBrowserSpeechRecognition);

    return () => {
      // Remove event listeners
      window.electron?.ipcRenderer.removeListener('speech-transcription', handleTranscription);
      window.electron?.ipcRenderer.removeListener('speech-transcription-error', handleTranscriptionError);
      window.electron?.ipcRenderer.removeListener('gemini-live-response', handleGeminiResponse);
      window.electron?.ipcRenderer.removeListener('gemini-live-error', handleGeminiError);
      window.electron?.ipcRenderer.removeListener('use-browser-speech-recognition', handleUseBrowserSpeechRecognition);

      // Clean up system audio stream
      if (systemAudioStreamRef.current) {
        systemAudioStreamRef.current.getTracks().forEach(track => track.stop());
        systemAudioStreamRef.current = null;
      }

      // Clean up combined stream
      if (combinedStreamRef.current) {
        combinedStreamRef.current.getTracks().forEach(track => track.stop());
        combinedStreamRef.current = null;
      }
    };
  }, []);

  // Initialize system audio capture with audio level detection
  const initializeSystemAudio = async () => {
    try {
      console.log('Requesting system audio stream...');

      // Use navigator.mediaDevices directly to get system audio
      try {
        // Request display media with audio - show a clear message to the user
        showToast("System Audio", "Please select 'Share system audio' when prompted", "neutral");

        // Request display media with audio
        const displayStream = await navigator.mediaDevices.getDisplayMedia({
          audio: {
            // Specify audio constraints for better quality
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
            // Request higher sample rate and bit depth for better quality
            sampleRate: 48000,
            sampleSize: 16
          },
          video: true // We need to request video, but we'll disable it
        });

        // Verify we have audio tracks
        if (!displayStream.getAudioTracks().length) {
          console.error('No audio tracks found in system audio stream');
          showToast("Warning", "System audio capture failed - no audio tracks. Make sure to check 'Share system audio'", "error");
          setIsCapturingSystemAudio(false);
          return false;
        }

        // Disable all video tracks - we only want audio
        displayStream.getVideoTracks().forEach((track: MediaStreamTrack) => {
          track.enabled = false;
          // Stop the video track after a short delay to ensure audio keeps working
          setTimeout(() => {
            try {
              track.stop();
              console.log('Video track stopped');
            } catch (e) {
              console.error('Error stopping video track:', e);
            }
          }, 1000);
        });

        console.log('System audio stream obtained successfully');
        console.log('Audio tracks:', displayStream.getAudioTracks().length);

        // Log audio track settings
        displayStream.getAudioTracks().forEach((track: MediaStreamTrack) => {
          console.log('System audio track settings:', track.getSettings());
          console.log('System audio track constraints:', track.getConstraints());
          console.log('Track label:', track.label);

          // Check if track is actually receiving audio
          try {
            const settings = track.getSettings();
            console.log('Track settings:', settings);
            if (settings.deviceId) {
              console.log('Device ID present, likely a valid audio source');
            }
          } catch (e) {
            console.error('Error checking track settings:', e);
          }
        });

        systemAudioStreamRef.current = displayStream;
        setIsCapturingSystemAudio(true);
        setIsSystemAudioMode(true); // Explicitly set system audio mode to true

        // Set up audio analyzer for level detection
        setupAudioAnalyzer(displayStream);

        // Confirm to the user that system audio is now being captured
        showToast("Success", "System audio capture enabled - auto-detection active", "success");

        console.log('System audio capture initialized successfully, isCapturingSystemAudio:', true);
        console.log('Auto-detection enabled:', isAutoDetectEnabled);

        return true;
      } catch (error) {
        console.error('Failed to get system audio stream:', error);
        showToast("Warning", "System audio capture not available. Please try again and select 'Share system audio'", "error");
        setIsCapturingSystemAudio(false);
        setIsSystemAudioMode(false);
        return false;
      }
    } catch (error) {
      console.error('Error initializing system audio:', error);
      showToast("Warning", "System audio capture not available", "error");
      setIsCapturingSystemAudio(false);
      setIsSystemAudioMode(false);
      return false;
    }
  };

  // Set up audio analyzer to detect audio levels
  const setupAudioAnalyzer = (stream: MediaStream) => {
    try {
      console.log('Setting up audio analyzer for stream with', stream.getAudioTracks().length, 'audio tracks');

      // Verify we have audio tracks before proceeding
      if (!stream.getAudioTracks().length) {
        console.error('No audio tracks in stream, cannot set up analyzer');
        showToast("Warning", "No audio tracks available for monitoring", "error");
        return;
      }

      // Create audio context
      const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
      if (!AudioContext) {
        console.error('AudioContext not supported in this browser');
        showToast("Warning", "Your browser doesn't support audio analysis", "error");
        return;
      }

      // Close existing audio context if it exists
      if (audioContextRef.current) {
        try {
          audioContextRef.current.close();
          console.log('Closed existing audio context');
        } catch (e) {
          console.error('Error closing existing audio context:', e);
        }
      }

      // Create new audio context
      audioContextRef.current = new AudioContext();
      console.log('Created new AudioContext, state:', audioContextRef.current.state);

      // Resume the audio context if it's suspended (needed in some browsers)
      if (audioContextRef.current.state === 'suspended') {
        audioContextRef.current.resume().then(() => {
          console.log('AudioContext resumed successfully');
        }).catch(err => {
          console.error('Failed to resume AudioContext:', err);
        });
      }

      // Create analyzer node with more sensitive settings
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 1024; // Increased from 256 for better frequency resolution
      analyserRef.current.smoothingTimeConstant = 0.5; // Reduced from 0.8 for faster response
      analyserRef.current.minDecibels = -90; // Lower threshold to detect quieter sounds
      analyserRef.current.maxDecibels = -10; // Upper threshold

      // Connect stream to analyzer
      try {
        const source = audioContextRef.current.createMediaStreamSource(stream);
        source.connect(analyserRef.current);
        console.log('Connected media stream to analyzer node');

        // Test the analyzer to make sure it's working
        const testData = new Uint8Array(analyserRef.current.frequencyBinCount);
        analyserRef.current.getByteFrequencyData(testData);

        // Calculate average of test data
        const sum = testData.reduce((acc, val) => acc + val, 0);
        const avg = sum / testData.length;
        console.log('Initial audio level test:', avg.toFixed(2));
      } catch (e) {
        console.error('Error connecting stream to analyzer:', e);
        showToast("Warning", "Failed to connect audio stream for monitoring", "error");
        return;
      }

      // Start monitoring audio levels
      startAudioLevelMonitoring();

      console.log('Audio analyzer set up successfully');
      showToast("Ready", "Audio level monitoring active", "neutral");
    } catch (error) {
      console.error('Error setting up audio analyzer:', error);
      showToast("Warning", "Failed to set up audio monitoring", "error");
    }
  };

  // Monitor audio levels and auto-enable SST when sound is detected
  const startAudioLevelMonitoring = () => {
    if (!analyserRef.current) {
      console.error('Audio analyzer not initialized');
      return;
    }

    console.log('Starting audio level monitoring, auto-detect enabled:', isAutoDetectEnabled);
    console.log('Continuous mode enabled:', isContinuousModeEnabled);

    const bufferLength = analyserRef.current.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    // Start continuous processing immediately if continuous mode is enabled
    if (isContinuousModeEnabled && isCapturingSystemAudio && !isListening) {
      console.log('CONTINUOUS MODE - Starting recording immediately');
      showToast("Continuous Mode", "Starting continuous system audio processing", "success");

      // Small delay to ensure everything is initialized
      setTimeout(() => {
        try {
          console.log('Calling startRecording(true) from continuous mode');
          startRecording(true); // Start recording in system audio mode
        } catch (error) {
          console.error('Error in continuous mode start recording:', error);
          showToast("Error", "Failed to start continuous recording", "error");
        }
      }, 500);
    }

    const checkAudioLevel = () => {
      if (!analyserRef.current) {
        console.error('Analyzer reference lost');
        requestAnimationFrame(checkAudioLevel);
        return;
      }

      // Get current audio data
      analyserRef.current.getByteFrequencyData(dataArray);

      // Calculate average level
      let sum = 0;
      for (let i = 0; i < bufferLength; i++) {
        sum += dataArray[i];
      }
      const average = sum / bufferLength;

      // Update audio level for visualization
      setAudioLevel(average);

      // Log audio level periodically (every ~2 seconds to avoid console spam)
      if (Math.random() < 0.01) {
        console.log('Current audio level:', average.toFixed(2),
                    'Auto-detect:', isAutoDetectEnabled,
                    'Continuous mode:', isContinuousModeEnabled,
                    'System audio captured:', isCapturingSystemAudio,
                    'Currently listening:', isListening);
      }

      // Audio detection threshold - ultra-sensitive for autonomous mode
      const threshold = isAutonomousMode ? 2 : 5; // Ultra-sensitive in autonomous mode

      // In continuous mode, we don't stop recording based on audio levels
      if (isContinuousModeEnabled) {
        // Just update the audio detection state for UI purposes
        setIsSystemAudioDetected(average > threshold);

        // If we're in continuous mode but not listening (recording stopped for some reason),
        // restart the recording
        if (isCapturingSystemAudio && !isListening) {
          console.log('CONTINUOUS MODE - Restarting recording');

          // Avoid spamming restart attempts by adding a small delay
          if (!audioDetectionTimeoutRef.current) {
            audioDetectionTimeoutRef.current = setTimeout(() => {
              try {
                console.log('Restarting recording in continuous mode');
                startRecording(true); // Start recording in system audio mode
              } catch (error) {
                console.error('Error restarting recording in continuous mode:', error);
              }
              audioDetectionTimeoutRef.current = null;
            }, 1000);
          }
        }
      } else {
        // Standard auto-detection mode (only if continuous mode is disabled)
        // Check if audio is above threshold
        if (average > threshold) {
          // Audio detected
          if (!isSystemAudioDetected) {
            console.log('System audio detected, level:', average.toFixed(2));
            console.log('Auto-detect enabled:', isAutoDetectEnabled);
            console.log('Currently listening:', isListening);
            console.log('System audio captured:', isCapturingSystemAudio);

            setIsSystemAudioDetected(true);

            // Auto-start recording if enabled and not already recording
            console.log('AUTO-DETECTION CHECK - Current state:', {
              isAutoDetectEnabled,
              isListening,
              isCapturingSystemAudio,
              audioLevel,
              isSystemAudioDetected
            });

            if (isAutoDetectEnabled && !isListening && isCapturingSystemAudio) {
              console.log('AUTO-DETECTION TRIGGERED - Starting recording now');
              showToast("Auto-Detect", "System audio detected - starting transcription", "neutral");

              // Force a small delay before starting recording to ensure state is updated
              setTimeout(() => {
                try {
                  console.log('Calling startRecording(true) from auto-detection');
                  startRecording(true); // Start recording in system audio mode
                } catch (error) {
                  console.error('Error in auto-start recording:', error);
                  showToast("Error", "Failed to auto-start recording", "error");
                }
              }, 100);
            } else {
              console.log('Not auto-starting recording because:',
                        !isAutoDetectEnabled ? 'Auto-detect disabled' :
                        isListening ? 'Already listening' :
                        !isCapturingSystemAudio ? 'System audio not captured' : 'Unknown reason');

              // Only show the audio detected toast occasionally to avoid spam
              if (Math.random() < 0.1) { // Only show this toast about 10% of the time
                showToast("Audio Detected", "System audio detected, but auto-recording not triggered", "neutral");
              }
            }

            // Clear any existing timeout
            if (audioDetectionTimeoutRef.current) {
              clearTimeout(audioDetectionTimeoutRef.current);
              audioDetectionTimeoutRef.current = null;
            }
          }
        } else {
          // Audio below threshold
          if (isSystemAudioDetected) {
            // Set a timeout to avoid stopping on brief pauses
            if (!audioDetectionTimeoutRef.current) {
              audioDetectionTimeoutRef.current = setTimeout(() => {
                console.log('System audio stopped, level:', average.toFixed(2));
                setIsSystemAudioDetected(false);

                // Auto-stop recording if it was auto-started
                if (isAutoDetectEnabled && isListening && isSystemAudioMode && !isContinuousModeEnabled) {
                  console.log('Auto-disabling system SST - stopping recording');
                  showToast("Auto-Detect", "System audio stopped - processing transcription", "neutral");
                  stopRecording();
                }

                audioDetectionTimeoutRef.current = null;
              }, 2000); // 2 second delay before considering audio stopped
            }
          }
        }
      }

      requestAnimationFrame(checkAudioLevel);
    };

    // Start monitoring
    checkAudioLevel();
  };

  // Start recording with specified mode
  const startRecording = (systemAudioMode: boolean = false) => {
    try {
      console.log('startRecording called with systemAudioMode =', systemAudioMode);
      console.log('Current mediaRecorderRef.current =', !!mediaRecorderRef.current);

      if (!mediaRecorderRef.current) {
        console.error('Media recorder not initialized');
        showToast("Error", "Media recorder not initialized. Try refreshing the app", "error");

        // We can't directly call initializeMediaRecorder since it's in a useEffect
        console.log('Media recorder not available - please refresh the app');
        showToast("Error", "Please refresh the application to reinitialize audio", "error");
        return;
      }

      // Check if already recording
      if (mediaRecorderRef.current.state === 'recording') {
        console.log('Already recording, stopping current recording first');
        try {
          mediaRecorderRef.current.stop();
          // Small delay to ensure the previous recording is fully stopped
          setTimeout(() => {
            startRecording(systemAudioMode); // Retry after stopping
          }, 500);
          return;
        } catch (stopError) {
          console.error('Error stopping existing recording:', stopError);
        }
      }

      // Clear previous chunks
      audioChunksRef.current = [];
      console.log('Cleared previous audio chunks');

      // Set the appropriate mode
      setIsSystemAudioMode(systemAudioMode);
      console.log('Set system audio mode to:', systemAudioMode);

      // Show a toast to indicate recording has started
      showToast(
        systemAudioMode ? "Recording System Audio" : "Recording Speech",
        systemAudioMode ? "Recording system audio..." : "Recording your speech...",
        "success" // Changed from neutral to success for better visibility
      );

      try {
        // Collect data more frequently for better quality
        console.log('Starting media recorder with state:', mediaRecorderRef.current.state);
        mediaRecorderRef.current.start(250); // Collect data every 250ms for more granular chunks
        console.log('Media recorder started successfully');

        // Update state to indicate we're listening
        setIsListening(true);
        console.log('Set isListening to true');
      } catch (error) {
        const startError = error as Error;
        console.error('Error starting media recorder:', startError);
        showToast("Error", `Failed to start recording: ${startError.message || 'Unknown error'}`, "error");

        // We can't directly reinitialize the media recorder
        setTimeout(() => {
          console.log('Recovery attempt - please refresh the app if recording fails');
          showToast("Warning", "Recording failed to start. Try refreshing the app", "error");
        }, 1000);
      }
    } catch (error) {
      const err = error as Error;
      console.error('Failed to start recording:', err);
      showToast("Error", `Failed to start recording: ${err.message || 'Unknown error'}`, "error");
    }
  };

  // Stop recording
  const stopRecording = () => {
    console.log('stopRecording called');
    console.log('mediaRecorderRef.current exists:', !!mediaRecorderRef.current);
    console.log('Continuous mode enabled:', isContinuousModeEnabled);

    // In continuous mode, we immediately restart recording after processing
    const shouldRestartAfterStop = isContinuousModeEnabled && isCapturingSystemAudio;

    if (mediaRecorderRef.current) {
      console.log('Current recorder state:', mediaRecorderRef.current.state);

      if (mediaRecorderRef.current.state === 'recording') {
        try {
          console.log('Stopping media recorder');
          mediaRecorderRef.current.stop();
          console.log('Media recorder stopped successfully');

          if (shouldRestartAfterStop) {
            showToast("Continuous Mode", "Processing audio chunk...", "neutral");
          } else {
            showToast("Recording Stopped", "Processing audio...", "neutral");
          }
        } catch (error) {
          const err = error as Error;
          console.error('Error stopping media recorder:', err);
          showToast("Error", `Failed to stop recording: ${err.message || 'Unknown error'}`, "error");
        } finally {
          // Always update the state even if there was an error
          setIsListening(false);
          console.log('Set isListening to false');

          // In continuous mode, restart recording after a short delay
          if (shouldRestartAfterStop) {
            console.log('Continuous mode active - will restart recording after processing');
            setTimeout(() => {
              if (isContinuousModeEnabled && isCapturingSystemAudio) {
                console.log('Restarting recording in continuous mode');
                startRecording(true);
              }
            }, 1000); // 1 second delay to allow processing to start
          }
        }
      } else {
        console.log('Media recorder is not in recording state, current state:', mediaRecorderRef.current.state);
        setIsListening(false);

        // If in continuous mode but not recording, restart
        if (shouldRestartAfterStop) {
          setTimeout(() => {
            console.log('Restarting recording in continuous mode (from non-recording state)');
            startRecording(true);
          }, 1000);
        }
      }
    } else {
      console.log('No media recorder available');
      setIsListening(false);
    }
  };

  // Initialize media recorder for Deepgram with autonomous system audio
  useEffect(() => {
    const initializeMediaRecorder = async () => {
      try {
        console.log('🚀 AUTONOMOUS MODE: Initializing system audio capture automatically...');

        // Auto-start system audio capture if autonomous mode is enabled
        if (isAutonomousMode && autoStartSystemAudio) {
          console.log('🎯 AUTO-STARTING system audio capture...');
          showToast("Autonomous Mode", "Auto-starting system audio capture...", "neutral");
        }

        // First try to get system audio
        const hasSystemAudio = await initializeSystemAudio();

        // If autonomous mode and system audio successful, auto-enable features
        if (isAutonomousMode && hasSystemAudio) {
          console.log('✅ System audio initialized successfully - enabling autonomous features');
          setIsContinuousModeEnabled(true);
          setIsAutoDetectEnabled(true);
          showToast("Autonomous Mode", "System audio capture active - continuous processing enabled", "success");
        }

        // Get microphone audio
        const micStream = await navigator.mediaDevices.getUserMedia({ audio: true });

        // If we have system audio, use it as the primary stream or combine with mic
        let finalStream = micStream;
        if (hasSystemAudio && systemAudioStreamRef.current) {
          console.log('System audio available, setting up for processing');

          // Check if system audio has tracks
          const systemAudioTracks = systemAudioStreamRef.current.getAudioTracks();
          if (systemAudioTracks.length > 0) {
            console.log('System audio tracks found:', systemAudioTracks.length);

            // Create a new MediaStream for the combined audio
            const combinedStream = new MediaStream();

            // Add system audio tracks first (prioritize system audio)
            systemAudioTracks.forEach((track: MediaStreamTrack) => {
              console.log('Adding system audio track:', track.label || 'Unnamed track');
              combinedStream.addTrack(track);
            });

            // Add microphone tracks
            micStream.getAudioTracks().forEach((track: MediaStreamTrack) => {
              console.log('Adding microphone track:', track.label || 'Unnamed track');
              combinedStream.addTrack(track);
            });

            finalStream = combinedStream;
            combinedStreamRef.current = combinedStream;

            // Log the final stream configuration
            console.log('Combined stream created with',
              combinedStream.getAudioTracks().length, 'audio tracks');

            // Set system audio mode to true since we're using system audio
            setIsSystemAudioMode(true);
          } else {
            console.warn('No audio tracks found in system audio stream');
            setIsSystemAudioMode(false);
          }
        } else {
          // Using only microphone
          console.log('Using microphone audio only');
          setIsSystemAudioMode(false);
        }

        // Check all supported MIME types
        console.log('Checking supported MIME types:');
        const allPossibleMimeTypes = [
          'audio/wav',
          'audio/webm',
          'audio/webm;codecs=opus',
          'audio/ogg',
          'audio/ogg;codecs=opus',
          'audio/mp4',
          'audio/mp3',
          'audio/pcm'
        ];

        // Log all supported types
        allPossibleMimeTypes.forEach(type => {
          console.log(`${type}: ${MediaRecorder.isTypeSupported(type) ? 'Supported' : 'Not supported'}`);
        });

        // Default to WebM since it's widely supported and works well with Deepgram
        let mimeType = 'audio/webm';

        // Prioritize formats in this order
        if (MediaRecorder.isTypeSupported('audio/webm;codecs=opus')) {
          mimeType = 'audio/webm;codecs=opus';
          console.log('Using audio/webm with opus codec (best compatibility with Deepgram)');
        } else if (MediaRecorder.isTypeSupported('audio/webm')) {
          mimeType = 'audio/webm';
          console.log('Using audio/webm format');
        } else if (MediaRecorder.isTypeSupported('audio/wav')) {
          mimeType = 'audio/wav';
          console.log('Using audio/wav format');
        } else if (MediaRecorder.isTypeSupported('audio/ogg;codecs=opus')) {
          mimeType = 'audio/ogg;codecs=opus';
          console.log('Using audio/ogg with opus codec');
        } else if (MediaRecorder.isTypeSupported('audio/ogg')) {
          mimeType = 'audio/ogg';
          console.log('Using audio/ogg format');
        }

        console.log('Selected MIME type:', mimeType);

        // Use specific audio format that Deepgram can process
        mediaRecorderRef.current = new MediaRecorder(finalStream, {
          mimeType: mimeType,
          audioBitsPerSecond: 128000 // 128 kbps audio quality
        });

        console.log('Media recorder initialized with mime type:', mediaRecorderRef.current.mimeType);

        mediaRecorderRef.current.ondataavailable = (event) => {
          if (event.data.size > 0) {
            console.log('Received audio chunk of size:', event.data.size);
            audioChunksRef.current.push(event.data);
          }
        };

        mediaRecorderRef.current.onstop = async () => {
          if (audioChunksRef.current.length > 0) {
            console.log('Processing', audioChunksRef.current.length, 'audio chunks');
            console.log('Continuous mode enabled:', isContinuousModeEnabled);

            // Create blob with explicit mime type
            // Get the actual MIME type from the recorder, or default to WebM
            const mimeType = mediaRecorderRef.current?.mimeType || 'audio/webm';
            console.log('Creating audio blob with mime type:', mimeType);

            // Log the first few bytes of the first chunk to help with debugging
            if (audioChunksRef.current.length > 0) {
              try {
                const reader = new FileReader();
                reader.onload = function() {
                  const arrayBuffer = this.result;
                  if (arrayBuffer) {
                    const bytes = new Uint8Array(arrayBuffer as ArrayBuffer);
                    if (bytes.length > 20) {
                      const hexBytes = Array.from(bytes.slice(0, 20))
                        .map(b => b.toString(16).padStart(2, '0'))
                        .join(' ');
                      console.log('First 20 bytes of audio data:', hexBytes);

                      // Check if it's WebM format
                      const isWebM = bytes.length > 4 &&
                                    bytes[0] === 0x1A &&
                                    bytes[1] === 0x45 &&
                                    bytes[2] === 0xDF &&
                                    bytes[3] === 0xA3;
                      console.log('Audio data appears to be WebM format:', isWebM);
                    }
                  }
                };
                reader.readAsArrayBuffer(audioChunksRef.current[0]);
              } catch (e) {
                console.error('Error reading audio chunk:', e);
              }
            }

            const audioBlob = new Blob(audioChunksRef.current, { type: mimeType });
            console.log('Audio blob created, size:', audioBlob.size);

            // In continuous mode, we process even small audio chunks
            const minSize = isContinuousModeEnabled ? 500 : 1000;

            // Only process if we have a reasonable amount of audio data
            if (audioBlob.size < minSize) {
              console.log(`Audio blob too small (${audioBlob.size} bytes), not processing`);

              if (isContinuousModeEnabled) {
                console.log('In continuous mode - skipping this chunk but continuing recording');
                // Don't show a toast in continuous mode to avoid spam
              } else {
                showToast("Warning", "Audio recording too short, please try again", "neutral");
              }

              audioChunksRef.current = [];
              setIsProcessing(false);
              return;
            }

            // For debugging - create an audio element to verify the audio
            try {
              const audioUrl = URL.createObjectURL(audioBlob);
              const audio = new Audio(audioUrl);
              console.log('Created audio element for debugging, URL:', audioUrl);

              // Log when audio can be played
              audio.oncanplaythrough = () => {
                console.log('Audio can be played, duration:', audio.duration, 'seconds');
                // Revoke the URL after we've verified it
                setTimeout(() => URL.revokeObjectURL(audioUrl), 1000);
              };

              // Log any errors
              audio.onerror = (e) => {
                console.error('Error with audio element:', e);
              };
            } catch (e) {
              console.error('Error creating audio element:', e);
            }

            // Convert to buffer
            const arrayBuffer = await audioBlob.arrayBuffer();
            const audioBuffer = new Uint8Array(arrayBuffer);
            console.log('Audio buffer created, length:', audioBuffer.length);

            // Process the audio with Deepgram via Electron
            setIsProcessing(true);
            try {
              // In continuous mode, we process smaller chunks
              const minBufferSize = isContinuousModeEnabled ? 2500 : 5000;

              // Make sure we have enough audio data
              if (audioBuffer.length > minBufferSize) {
                console.log('Processing audio buffer with length:', audioBuffer.length);

                try {
                  // Determine if this is user speech or system audio
                  const isUserSpeech = !isSystemAudioMode;
                  console.log(`Processing ${isUserSpeech ? 'user speech' : 'system audio'}`);

                  // Log detailed information about the audio being processed
                  console.log('Audio buffer size:', audioBuffer.length);
                  console.log('Audio mode:', isSystemAudioMode ? 'System Audio' : 'User Microphone');
                  console.log('Current language:', currentLanguage);

                  // Show a toast to indicate processing is starting
                  // In continuous mode, only show occasional toasts to avoid spam
                  if (!isContinuousModeEnabled || Math.random() < 0.2) { // Show toast only 20% of the time in continuous mode
                    showToast(
                      isUserSpeech ? "Processing Speech" : "Processing System Audio",
                      isContinuousModeEnabled ? "Continuous transcription in progress..." : "Transcribing audio...",
                      "neutral"
                    );
                  }

                  try {
                    // First try with empty transcript to force Deepgram to transcribe the audio
                    // Pass the isUserSpeech flag to ensure proper processing
                    const result = await window.electronAPI.processVoiceInput('', currentLanguage, audioBuffer, isUserSpeech);

                    if (result.success && result.response) {
                      console.log('Successfully processed audio with response:', result.response.substring(0, 100) + '...');

                      // In continuous mode, we append responses instead of replacing them
                      if (isContinuousModeEnabled && response) {
                        // Add a separator between responses
                        setResponse(prev => {
                          // Limit the size of the response to prevent it from growing too large
                          const maxLength = 5000; // Maximum characters to keep
                          let newResponse = prev;

                          // If the previous response is too long, trim it
                          if (prev.length > maxLength) {
                            // Keep only the last part of the previous response
                            newResponse = "...\n" + prev.substring(prev.length - maxLength);
                          }

                          // Add the new response with a separator
                          return newResponse + "\n\n--- New Response ---\n\n" + result.response;
                        });
                      } else {
                        // Normal mode - replace the response
                        setResponse(result.response);
                      }

                      // If we got a response but no transcript, update the transcript with what was recognized
                      if ('transcript' in result && result.transcript) {
                        console.log('Setting transcript from result:', result.transcript);

                        // In continuous mode, we append transcripts
                        if (isContinuousModeEnabled && transcript) {
                          setTranscript(prev => {
                            // Limit the size of the transcript to prevent it from growing too large
                            const maxLength = 1000; // Maximum characters to keep
                            let newTranscript = prev;

                            // If the previous transcript is too long, trim it
                            if (prev.length > maxLength) {
                              // Keep only the last part of the previous transcript
                              newTranscript = "...\n" + prev.substring(prev.length - maxLength);
                            }

                            // Add the new transcript with a separator
                            return newTranscript + " " + (result.transcript as string);
                          });
                        } else {
                          // Normal mode - replace the transcript
                          setTranscript(result.transcript as string);
                        }
                      }
                    } else {
                      console.warn('Initial processing failed or returned empty response');

                      // If that fails, try with any existing transcript
                      if (transcript) {
                        console.log('Falling back to existing transcript:', transcript);

                        if (!isContinuousModeEnabled) {
                          showToast("Fallback", "Using existing transcript...", "neutral");
                        }

                        const fallbackResult = await window.electronAPI.processVoiceInput(
                          transcript,
                          currentLanguage,
                          undefined,
                          isUserSpeech
                        );

                        if (fallbackResult.success && fallbackResult.response) {
                          console.log('Fallback processing succeeded');

                          // In continuous mode, we append responses
                          if (isContinuousModeEnabled && response) {
                            setResponse(prev => prev + "\n\n--- New Response ---\n\n" + fallbackResult.response);
                          } else {
                            setResponse(fallbackResult.response);
                          }
                        } else {
                          console.error('Fallback processing failed:', fallbackResult.error);

                          if (!isContinuousModeEnabled) {
                            handleTranscriptionFailure();
                          }
                        }
                      } else {
                        console.error('No transcript available for fallback');

                        if (!isContinuousModeEnabled) {
                          handleTranscriptionFailure();
                        }
                      }
                    }
                  } catch (error) {
                    console.error('Error during audio processing:', error);

                    if (!isContinuousModeEnabled) {
                      handleTranscriptionFailure();
                    }
                  }

                  // Helper function to handle transcription failure
                  function handleTranscriptionFailure() {
                    showToast("Warning", "Speech recognition failed. Please type your question below:", "neutral");

                    // Create a simple input for the user to type their question
                    setTranscript(""); // Clear any existing transcript
                    setTimeout(() => {
                      // Focus on the transcript input after a short delay
                      const inputElement = document.getElementById('manual-transcript-input');
                      if (inputElement) {
                        inputElement.focus();
                      }
                    }, 500);
                  }
                } catch (error) {
                  console.error('Error in voice processing:', error);

                  if (!isContinuousModeEnabled) {
                    showToast("Warning", "Speech recognition failed. Please type your question below:", "neutral");
                  }
                }
              } else {
                console.log('Audio buffer too small, not sending to Deepgram');

                if (!isContinuousModeEnabled) {
                  showToast("Warning", "Audio recording too short, please try again", "neutral");
                }
              }
            } catch (error) {
              console.error('Error processing audio:', error);

              if (!isContinuousModeEnabled) {
                showToast("Error", "Failed to process audio", "error");
              }
            } finally {
              setIsProcessing(false);
            }

            // Clear audio chunks for next recording
            audioChunksRef.current = [];
          }
        };

        console.log('Media recorder initialized successfully');
        setIsUsingDeepgram(true);
      } catch (error) {
        console.error('Failed to initialize media recorder:', error);
        showToast("Warning", "Using browser speech recognition as fallback", "neutral");
        initializeBrowserSpeechRecognition();
        setIsUsingDeepgram(false);
      }
    };

    initializeMediaRecorder();

    // Cleanup function
    return () => {
      // Stop recording if active
      if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
        mediaRecorderRef.current.stop();
      }

      // Clean up audio analyzer
      if (audioContextRef.current) {
        try {
          audioContextRef.current.close();
          console.log('Audio context closed');
        } catch (error) {
          console.error('Error closing audio context:', error);
        }
      }

      // Clear any pending timeouts
      if (audioDetectionTimeoutRef.current) {
        clearTimeout(audioDetectionTimeoutRef.current);
        audioDetectionTimeoutRef.current = null;
      }
    };
  }, []);

  // Load saved data from localStorage on component mount
  useEffect(() => {
    try {
      // Load saved transcript
      const savedTranscript = localStorage.getItem('savedTranscript');
      if (savedTranscript) {
        setTranscript(savedTranscript);
      }

      // Load saved resume and job description
      const savedResumeInfo = localStorage.getItem('resumeInfo');
      const savedJobDescription = localStorage.getItem('jobDescription');

      if (savedResumeInfo) {
        setResumeInfo(savedResumeInfo);
      }

      if (savedJobDescription) {
        setJobDescription(savedJobDescription);
      }

      // Load auto-detection preference
      const savedAutoDetect = localStorage.getItem('autoDetectEnabled');
      if (savedAutoDetect !== null) {
        setIsAutoDetectEnabled(savedAutoDetect === 'true');
      }

      // Load continuous mode preference
      const savedContinuousMode = localStorage.getItem('continuousModeEnabled');
      if (savedContinuousMode !== null) {
        setIsContinuousModeEnabled(savedContinuousMode === 'true');
        console.log('Loaded continuous mode preference:', savedContinuousMode === 'true');
      }
    } catch (error) {
      console.error('Error loading saved data:', error);
    }
  }, []);

  // Save auto-detection preference when it changes
  useEffect(() => {
    try {
      localStorage.setItem('autoDetectEnabled', isAutoDetectEnabled.toString());
      console.log('Auto-detection preference saved:', isAutoDetectEnabled);
    } catch (error) {
      console.error('Error saving auto-detection preference:', error);
    }
  }, [isAutoDetectEnabled]);

  // Save continuous mode preference when it changes
  useEffect(() => {
    try {
      localStorage.setItem('continuousModeEnabled', isContinuousModeEnabled.toString());
      console.log('Continuous mode preference saved:', isContinuousModeEnabled);

      // If continuous mode is enabled, make sure auto-detection is also enabled
      if (isContinuousModeEnabled && !isAutoDetectEnabled) {
        setIsAutoDetectEnabled(true);
        localStorage.setItem('autoDetectEnabled', 'true');
        console.log('Auto-enabled auto-detection because continuous mode was enabled');
      }
    } catch (error) {
      console.error('Error saving continuous mode preference:', error);
    }
  }, [isContinuousModeEnabled, isAutoDetectEnabled]);

  // Autonomous startup effect - automatically start continuous processing
  useEffect(() => {
    if (isAutonomousMode && isContinuousModeEnabled && isCapturingSystemAudio && !isListening) {
      console.log('🤖 AUTONOMOUS STARTUP: Auto-starting continuous system audio processing...');

      // Delay to ensure everything is properly initialized
      const startupTimer = setTimeout(() => {
        if (isCapturingSystemAudio && !isListening && isContinuousModeEnabled) {
          console.log('🎯 Starting autonomous continuous recording...');
          startRecording(true); // Start in system audio mode
          showToast("Autonomous Active", "Continuous system audio processing started automatically", "success");
        }
      }, 3000); // 3 second delay for initialization

      return () => clearTimeout(startupTimer);
    }
  }, [isAutonomousMode, isContinuousModeEnabled, isCapturingSystemAudio, isListening]);

  // Auto-restart mechanism for continuous mode
  useEffect(() => {
    if (isContinuousModeEnabled && isCapturingSystemAudio && !isListening) {
      console.log('🔄 CONTINUOUS MODE: Detected recording stopped, auto-restarting...');

      const restartTimer = setTimeout(() => {
        if (isContinuousModeEnabled && isCapturingSystemAudio && !isListening) {
          console.log('🔄 Auto-restarting continuous recording...');
          startRecording(true);
        }
      }, 2000); // 2 second delay before restart

      return () => clearTimeout(restartTimer);
    }
  }, [isContinuousModeEnabled, isCapturingSystemAudio, isListening]);

  // Initialize browser speech recognition as fallback
  const initializeBrowserSpeechRecognition = () => {
    // Check if browser supports SpeechRecognition
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

    if (!SpeechRecognition) {
      showToast("Error", "Speech recognition is not supported in this browser.", "error");
      return;
    }

    recognitionRef.current = new SpeechRecognition();
    recognitionRef.current.continuous = true;
    recognitionRef.current.interimResults = true;
    recognitionRef.current.lang = 'en-US';

    recognitionRef.current.onresult = (event: any) => {
      let interimTranscript = '';
      let finalTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript;
        if (event.results[i].isFinal) {
          finalTranscript += transcript + ' ';
        } else {
          interimTranscript += transcript;
        }
      }

      setTranscript(finalTranscript || interimTranscript);
    };

    recognitionRef.current.onerror = (event: any) => {
      console.error('Speech recognition error', event.error);
      showToast("Error", `Speech recognition error: ${event.error}`, "error");
      setIsListening(false);
    };

    recognitionRef.current.onend = () => {
      if (isListening) {
        recognitionRef.current.start();
      }
    };
  };

  // Handle saving resume and job description data
  const handleSaveResumeJob = (newResumeInfo: string, newJobDescription: string) => {
    setResumeInfo(newResumeInfo);
    setJobDescription(newJobDescription);

    // Store in localStorage for persistence
    try {
      localStorage.setItem('resumeInfo', newResumeInfo);
      localStorage.setItem('jobDescription', newJobDescription);
      console.log('Resume and job info saved to localStorage');

      // Send to main process via custom event
      // This will be picked up by the main process if needed
      try {
        // Use a custom event to notify that we want to update resume/job info
        const customEvent = new CustomEvent('resume-job-info-updated', {
          detail: { resumeInfo: newResumeInfo, jobDescription: newJobDescription }
        });
        window.dispatchEvent(customEvent);

        // Also try to send via IPC if available
        if (window.electron && window.electron.ipcRenderer) {
          window.electron.ipcRenderer.send('update-resume-job-info', newResumeInfo, newJobDescription);
          console.log('Resume and job info sent to main process via IPC');
        }

        showToast("Success", "Resume and job info updated for AI responses", "success");
      } catch (sendError) {
        console.error('Error sending resume and job info to main process:', sendError);
        showToast("Warning", "Resume and job info saved locally but not sent to AI", "neutral");
      }
    } catch (error) {
      console.error('Error saving resume and job info:', error);
      showToast("Error", "Failed to save resume and job info", "error");
    }
  };

  // Update dimensions when content changes
  useEffect(() => {
    const updateDimensions = () => {
      if (contentRef.current) {
        const height = contentRef.current.scrollHeight;
        const width = contentRef.current.scrollWidth;
        window.electronAPI?.updateContentDimensions({ width, height });
      }
    };

    const resizeObserver = new ResizeObserver(updateDimensions);
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current);
    }

    // Also watch DOM changes
    const mutationObserver = new MutationObserver(updateDimensions);
    if (contentRef.current) {
      mutationObserver.observe(contentRef.current, {
        childList: true,
        subtree: true,
        attributes: true,
        characterData: true
      });
    }

    // Initial dimension update
    updateDimensions();

    return () => {
      resizeObserver.disconnect();
      mutationObserver.disconnect();
    };
  }, [transcript, response, resumeInfo, jobDescription]);

  const toggleListening = () => {
    if (isListening) {
      // Stop recording
      if (isUsingDeepgram) {
        stopRecording();
      } else if (recognitionRef.current) {
        recognitionRef.current.stop();
        setIsListening(false);
      }
    } else {
      try {
        // Start recording
        if (isUsingDeepgram && mediaRecorderRef.current) {
          // Determine if we should use system audio mode
          const useSystemAudioMode = isCapturingSystemAudio;

          // Start recording with the appropriate mode
          startRecording(useSystemAudioMode);

          // Set a minimum recording time to ensure we get enough audio data
          setTimeout(() => {
            console.log('Minimum recording time reached (3 seconds)');
            // We don't stop here, just let the user know they can stop if they want
            showToast("Ready", "Recording in progress - click Stop when finished", "neutral");

            // If the recording is very short, encourage the user to continue
            if (audioChunksRef.current.length < 10) {
              showToast("Tip", "For better results, record at least 5-10 seconds of audio", "neutral");
            }
          }, 3000);
        } else if (recognitionRef.current) {
          recognitionRef.current.start();
          setIsListening(true);
        } else {
          throw new Error("No speech recognition method available");
        }
      } catch (error) {
        console.error('Failed to start speech recognition:', error);
        showToast("Error", "Failed to start speech recognition", "error");
      }
    }
  };

  const processTranscript = async () => {
    if (!transcript.trim()) {
      showToast("Error", "No speech detected to process", "error");
      return;
    }

    setIsProcessing(true);

    try {
      // Use Gemini API to process the transcript
      const result = await window.electronAPI.processVoiceInput(transcript, currentLanguage);

      if (result.success) {
        setResponse(result.response || '');
      } else {
        showToast("Error", result.error || "Failed to process speech", "error");
      }
    } catch (error) {
      console.error('Error processing transcript:', error);
      showToast("Error", "Failed to process speech input", "error");
    } finally {
      setIsProcessing(false);
    }
  };

  const clearAll = () => {
    setTranscript("");
    setResponse("");
  };

  return (
    <div ref={contentRef} className="bg-gray-900 w-full max-w-4xl mx-auto">
      <div className="px-6 py-5">
        <div className="space-y-6">
          {/* Voice Input Section */}
          <div className="bg-gradient-to-r from-purple-600/10 to-blue-600/10 rounded-lg p-5 border border-purple-500/20">
            <div className="flex items-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-400 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z" />
                <path d="M19 10v2a7 7 0 0 1-14 0v-2" />
                <line x1="12" y1="19" x2="12" y2="23" />
                <line x1="8" y1="23" x2="16" y2="23" />
              </svg>
              <h2 className="text-lg font-semibold text-white">Voice Assistant</h2>

              <div className="ml-auto flex items-center space-x-3">
                {/* Autonomous Mode toggle */}
                <div className="flex items-center mr-2 bg-gray-800 p-1.5 rounded-md border border-green-500/30">
                  <label className="inline-flex items-center cursor-pointer">
                    <span className="mr-2 text-sm font-medium text-green-300">🤖 Autonomous</span>
                    <div className="relative">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={isAutonomousMode}
                        onChange={() => {
                          const newValue = !isAutonomousMode;
                          setIsAutonomousMode(newValue);

                          if (newValue) {
                            // Enable all autonomous features
                            setIsContinuousModeEnabled(true);
                            setIsAutoDetectEnabled(true);
                            setAutoStartSystemAudio(true);

                            showToast("Autonomous Mode", "Fully autonomous operation enabled - system will auto-capture and process audio", "success");

                            // Auto-start system audio if not already active
                            if (!isCapturingSystemAudio) {
                              setTimeout(() => initializeSystemAudio(), 1000);
                            }
                          } else {
                            showToast("Manual Mode", "Autonomous mode disabled - manual control enabled", "neutral");
                          }
                        }}
                      />
                      <div className="w-10 h-6 bg-gray-700 rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-gray-300 after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                    </div>
                    <span className="ml-2 text-xs text-gray-400">{isAutonomousMode ? "Active" : "Manual"}</span>
                  </label>
                </div>

                {/* Auto-detection toggle */}
                {isCapturingSystemAudio && (
                  <div className="flex items-center mr-2 bg-gray-800 p-1.5 rounded-md border border-blue-500/30">
                    <label className="inline-flex items-center cursor-pointer">
                      <span className="mr-2 text-sm font-medium text-blue-300">Auto-Detect</span>
                      <div className="relative">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={isAutoDetectEnabled}
                          onChange={() => {
                            const newValue = !isAutoDetectEnabled;
                            setIsAutoDetectEnabled(newValue);
                            showToast(
                              newValue ? "Auto-Detect Enabled" : "Auto-Detect Disabled",
                              newValue ? "System audio will be automatically transcribed when detected" : "Manual recording mode activated",
                              newValue ? "success" : "neutral"
                            );
                            // Save preference to localStorage
                            localStorage.setItem('autoDetectEnabled', newValue.toString());
                            console.log('Auto-detection set to:', newValue);
                          }}
                        />
                        <div className="w-10 h-6 bg-gray-700 rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-gray-300 after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </div>
                      <span className="ml-2 text-xs text-gray-400">{isAutoDetectEnabled ? "On" : "Off"}</span>
                    </label>
                  </div>
                )}

                {/* Continuous Mode toggle */}
                {isCapturingSystemAudio && (
                  <div className="flex items-center mr-2 bg-gray-800 p-1.5 rounded-md border border-purple-500/30">
                    <label className="inline-flex items-center cursor-pointer">
                      <span className="mr-2 text-sm font-medium text-purple-300">Continuous</span>
                      <div className="relative">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={isContinuousModeEnabled}
                          onChange={() => {
                            const newValue = !isContinuousModeEnabled;
                            setIsContinuousModeEnabled(newValue);

                            // If enabling continuous mode, also enable auto-detect
                            if (newValue && !isAutoDetectEnabled) {
                              setIsAutoDetectEnabled(true);
                              localStorage.setItem('autoDetectEnabled', 'true');
                            }

                            showToast(
                              newValue ? "Continuous Mode Enabled" : "Continuous Mode Disabled",
                              newValue ? "System audio will be continuously processed without interruption" : "Standard processing mode activated",
                              newValue ? "success" : "neutral"
                            );

                            // Save preference to localStorage
                            localStorage.setItem('continuousModeEnabled', newValue.toString());
                            console.log('Continuous mode set to:', newValue);

                            // If enabling continuous mode and not already recording, start recording
                            if (newValue && !isListening && isCapturingSystemAudio) {
                              console.log('Starting continuous recording immediately');
                              setTimeout(() => startRecording(true), 500);
                            }
                          }}
                        />
                        <div className="w-10 h-6 bg-gray-700 rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-gray-300 after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                      </div>
                      <span className="ml-2 text-xs text-gray-400">{isContinuousModeEnabled ? "On" : "Off"}</span>
                    </label>
                  </div>
                )}

                <button
                  onClick={toggleListening}
                  className={`flex items-center space-x-1 px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                    isListening
                      ? 'bg-red-500/20 text-red-300 hover:bg-red-500/30 border border-red-500/30'
                      : 'bg-green-500/20 text-green-300 hover:bg-green-500/30 border border-green-500/30'
                  }`}
                >
                  <span>{isListening ? 'Stop Listening' : 'Start Listening'}</span>
                  <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ${isListening ? 'text-red-400' : 'text-green-400'}`} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    {isListening ? (
                      <rect x="6" y="6" width="12" height="12" />
                    ) : (
                      <polygon points="5 3 19 12 5 21 5 3" />
                    )}
                  </svg>
                </button>
              </div>
            </div>

            {/* Transcript Display */}
            <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700 mb-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-gray-300">Your Speech</h3>
                <div className="flex items-center">
                  {isListening && (
                    <div className="flex items-center mr-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full mr-1 animate-pulse"></div>
                      <span className="text-xs text-red-400">Recording</span>
                    </div>
                  )}
                  {isCapturingSystemAudio && (
                    <div className="flex items-center mr-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-1 animate-pulse"></div>
                      <span className="text-xs text-blue-400">System Audio</span>
                    </div>
                  )}
                  {isSystemAudioDetected && (
                    <div className="flex items-center mr-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                      <span className="text-xs text-green-400">Audio Detected</span>
                    </div>
                  )}
                  {/* Audio level visualization */}
                  {isCapturingSystemAudio && (
                    <div className="flex flex-col items-center mr-3">
                      <div className="text-xs text-gray-400 mb-1">Audio Level</div>
                      <div className="flex items-center h-4 w-24 bg-gray-700 rounded-full overflow-hidden border border-gray-600">
                        <div
                          className={`h-full transition-all duration-100 ${
                            audioLevel > 15
                              ? 'bg-gradient-to-r from-green-500 to-blue-500'
                              : 'bg-gradient-to-r from-blue-500 to-purple-500'
                          }`}
                          style={{ width: `${Math.min(100, (audioLevel / 30) * 100)}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between w-24 mt-1">
                        <span className="text-xs text-gray-500">Low</span>
                        <span className="text-xs text-gray-500">High</span>
                      </div>
                    </div>
                  )}
                  <button
                    onClick={clearAll}
                    className="text-xs text-gray-400 hover:text-white transition-colors"
                  >
                    Clear All
                  </button>
                </div>
              </div>
              <div className="min-h-24 text-gray-300 whitespace-pre-wrap">
                {transcript ? (
                  <div>{transcript}</div>
                ) : (
                  <div className="flex flex-col gap-2">
                    <span className="text-gray-500 italic">Speech will appear here...</span>
                    <input
                      id="manual-transcript-input"
                      type="text"
                      className="bg-gray-700 text-white p-2 rounded-md border border-gray-600 w-full"
                      placeholder="Or type your question here and press Enter..."
                      value={transcript}
                      onChange={(e) => setTranscript(e.target.value)}
                      onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                        if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                          setTranscript(e.currentTarget.value);
                          processTranscript();
                        }
                      }}
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Manual Input Field */}
            {!transcript.trim() && !isListening && (
              <div className="mb-4">
                <div className="flex flex-col gap-2">
                  <label className="text-sm text-gray-400">Or type your question:</label>
                  <div className="flex gap-2">
                    <input
                      id="manual-transcript-input"
                      type="text"
                      className="flex-1 bg-gray-700 text-white p-2 rounded-md border border-gray-600"
                      placeholder="Type your question here..."
                      value={transcript}
                      onChange={(e) => setTranscript(e.target.value)}
                      onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                        if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                          processTranscript();
                        }
                      }}
                    />
                    <button
                      onClick={processTranscript}
                      disabled={!transcript.trim() || isProcessing}
                      className="bg-blue-600/20 text-blue-300 hover:bg-blue-600/30 border border-blue-600/30 px-3 py-2 rounded-md"
                    >
                      Send
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Process Button */}
            <div className="flex justify-center mb-4">
              <button
                onClick={processTranscript}
                disabled={!transcript.trim() || isProcessing}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  !transcript.trim() || isProcessing
                    ? 'bg-gray-700/50 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-600/20 text-blue-300 hover:bg-blue-600/30 border border-blue-600/30'
                }`}
              >
                {isProcessing ? (
                  <>
                    <svg className="animate-spin h-4 w-4 text-blue-400 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>Processing...</span>
                  </>
                ) : (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M22 2L11 13"></path>
                      <path d="M22 2L15 22L11 13L2 9L22 2Z"></path>
                    </svg>
                    <span>Process Speech</span>
                  </>
                )}
              </button>
            </div>

            {/* Response Display */}
            {response && (
              <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700">
                <h3 className="text-sm font-medium text-gray-300 mb-2">AI Response</h3>
                <div className="text-gray-300 whitespace-pre-wrap">
                  {response}
                </div>
              </div>
            )}
          </div>

          {/* Resume & Job Description Input */}
          <ResumeJobInput
            onSave={handleSaveResumeJob}
            initialResumeInfo={resumeInfo}
            initialJobDescription={jobDescription}
          />

          {/* System Audio Manual Control */}
          {isCapturingSystemAudio && (
            <div className="bg-blue-900/20 rounded-lg p-4 border border-blue-700/30 mb-4">
              <div className="flex items-center mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
                  <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"></path>
                </svg>
                <h3 className="text-sm font-medium text-blue-300">System Audio Controls</h3>
              </div>

              <div className="flex flex-col space-y-3">
                <p className="text-xs text-gray-300">
                  If auto-detection isn't working, you can manually control system audio recording:
                </p>

                <div className="flex space-x-3">
                  <button
                    onClick={() => {
                      if (!isListening) {
                        console.log('Manual system audio recording triggered');
                        showToast("Manual Control", "Starting system audio recording", "neutral");
                        startRecording(true); // Force system audio mode
                      } else {
                        console.log('Manual stop recording triggered');
                        showToast("Manual Control", "Stopping recording", "neutral");
                        stopRecording();
                      }
                    }}
                    className={`flex-1 flex items-center justify-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      isListening
                        ? 'bg-red-500/20 text-red-300 hover:bg-red-500/30 border border-red-500/30'
                        : 'bg-blue-600/20 text-blue-300 hover:bg-blue-600/30 border border-blue-600/30'
                    }`}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      {isListening ? (
                        <rect x="6" y="6" width="12" height="12" />
                      ) : (
                        <polygon points="5 3 19 12 5 21 5 3" />
                      )}
                    </svg>
                    <span>{isListening ? 'Stop System Recording' : 'Start System Recording'}</span>
                  </button>
                </div>

                <div className="text-xs text-gray-400 italic">
                  Use this if auto-detection isn't working but you can see audio levels changing.
                </div>
              </div>
            </div>
          )}

          {/* Quick Help Section */}
          <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                <line x1="12" y1="17" x2="12.01" y2="17"></line>
              </svg>
              <h3 className="text-sm font-medium text-gray-300">Quick Help</h3>
              <div className="ml-auto flex space-x-2">
                <span className={`text-xs px-2 py-1 rounded-full ${isUsingDeepgram ? 'bg-green-500/20 text-green-300' : 'bg-yellow-500/20 text-yellow-300'}`}>
                  {isUsingDeepgram ? 'Using Deepgram' : 'Using Browser API'}
                </span>
                {isCapturingSystemAudio && (
                  <span className="text-xs px-2 py-1 rounded-full bg-blue-500/20 text-blue-300">
                    System Audio Enabled
                  </span>
                )}
              </div>
            </div>
            <ul className="text-xs text-gray-400 space-y-1 ml-6 list-disc">
              <li>Click "Start Listening" to begin voice recognition</li>
              <li>Speak clearly into your microphone</li>
              {isCapturingSystemAudio && (
                <>
                  <li className="text-blue-300">System audio is being captured (YouTube, video calls, etc.)</li>
                  <li className="text-green-300">
                    <strong>Auto-Detect:</strong> When enabled, system audio will be automatically transcribed when detected
                  </li>
                  <li className="text-purple-300">
                    <strong>Continuous Mode:</strong> When enabled, system audio will be continuously processed without interruption
                  </li>
                  <li className="text-green-300">
                    The audio level meter shows when system audio is playing
                  </li>
                  <li className="text-yellow-300">
                    <strong>Troubleshooting:</strong> If auto-detection doesn't work, try enabling Continuous Mode or use the manual "Start System Recording" button
                  </li>
                </>
              )}
              <li>Click "Process Speech" to get AI assistance</li>
              <li>Or type your question directly in the input field</li>
              <li>Add your resume and job description to get tailored interview responses</li>
              <li>Press <kbd className="px-1.5 py-0.5 bg-gray-700 border border-gray-600 rounded text-gray-300 text-xs">{COMMAND_KEY}+B</kbd> to toggle the window visibility</li>
              <li>Use <kbd className="px-1.5 py-0.5 bg-gray-700 border border-gray-600 rounded text-gray-300 text-xs">{COMMAND_KEY}+Arrow</kbd> keys to move the window</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Voice;
