import React, { useState } from 'react';
import { Button } from './ui/button';

interface CodeComparisonProps {
  originalCode: string;
  language: string;
}

interface AlternativeSolution {
  id: string;
  code: string;
  approach: string;
  timeComplexity: string;
  spaceComplexity: string;
  pros: string[];
  cons: string[];
}

const CodeComparison: React.FC<CodeComparisonProps> = ({ originalCode, language }) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [alternativeSolutions, setAlternativeSolutions] = useState<AlternativeSolution[]>([]);
  const [selectedSolution, setSelectedSolution] = useState<string | null>(null);
  
  const generateAlternatives = async () => {
    if (!originalCode || isGenerating) return;
    
    setIsGenerating(true);
    
    try {
      // In a real implementation, this would call the Gemini API
      // For now, we'll simulate the generation with a timeout
      setTimeout(() => {
        const simulatedSolutions: AlternativeSolution[] = [
          {
            id: '1',
            code: `// Dynamic Programming Approach
function solution(nums) {
  if (!nums || nums.length === 0) return 0;
  
  const dp = new Array(nums.length).fill(0);
  dp[0] = nums[0];
  
  for (let i = 1; i < nums.length; i++) {
    dp[i] = Math.max(nums[i], dp[i-1] + nums[i]);
  }
  
  return Math.max(...dp);
}`,
            approach: 'Dynamic Programming',
            timeComplexity: 'O(n)',
            spaceComplexity: 'O(n)',
            pros: ['Easy to understand', 'Handles edge cases well', 'Optimal time complexity'],
            cons: ['Uses extra space for DP array', 'Not as intuitive for beginners']
          },
          {
            id: '2',
            code: `// Greedy Approach
function solution(nums) {
  if (!nums || nums.length === 0) return 0;
  
  let maxSoFar = nums[0];
  let maxEndingHere = nums[0];
  
  for (let i = 1; i < nums.length; i++) {
    maxEndingHere = Math.max(nums[i], maxEndingHere + nums[i]);
    maxSoFar = Math.max(maxSoFar, maxEndingHere);
  }
  
  return maxSoFar;
}`,
            approach: 'Greedy Algorithm (Kadane\'s Algorithm)',
            timeComplexity: 'O(n)',
            spaceComplexity: 'O(1)',
            pros: ['Optimal space complexity', 'Single pass through the array', 'No extra data structures'],
            cons: ['Slightly harder to understand the intuition', 'Requires careful implementation']
          },
          {
            id: '3',
            code: `// Divide and Conquer Approach
function solution(nums) {
  if (!nums || nums.length === 0) return 0;
  
  return maxSubArray(nums, 0, nums.length - 1);
}

function maxSubArray(nums, left, right) {
  if (left === right) return nums[left];
  
  const mid = Math.floor((left + right) / 2);
  
  const leftSum = maxSubArray(nums, left, mid);
  const rightSum = maxSubArray(nums, mid + 1, right);
  const crossSum = maxCrossingSum(nums, left, mid, right);
  
  return Math.max(leftSum, rightSum, crossSum);
}

function maxCrossingSum(nums, left, mid, right) {
  let leftSum = Number.MIN_SAFE_INTEGER;
  let sum = 0;
  
  for (let i = mid; i >= left; i--) {
    sum += nums[i];
    leftSum = Math.max(leftSum, sum);
  }
  
  sum = 0;
  let rightSum = Number.MIN_SAFE_INTEGER;
  
  for (let i = mid + 1; i <= right; i++) {
    sum += nums[i];
    rightSum = Math.max(rightSum, sum);
  }
  
  return leftSum + rightSum;
}`,
            approach: 'Divide and Conquer',
            timeComplexity: 'O(n log n)',
            spaceComplexity: 'O(log n) - call stack',
            pros: ['Works well for parallel processing', 'Good for educational purposes', 'Demonstrates recursive approach'],
            cons: ['Not optimal time complexity', 'More complex implementation', 'Uses more space due to recursion']
          }
        ];
        
        setAlternativeSolutions(simulatedSolutions);
        setSelectedSolution(simulatedSolutions[0].id);
        setIsGenerating(false);
      }, 2000);
    } catch (error) {
      console.error('Error generating alternative solutions:', error);
      setIsGenerating(false);
    }
  };

  const selectedSolutionData = alternativeSolutions.find(s => s.id === selectedSolution);

  return (
    <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700 mb-4">
      <h3 className="text-sm font-medium text-amber-400 mb-3 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M16 16l4-4-4-4"></path>
          <path d="M8 8l-4 4 4 4"></path>
          <path d="M21 12H3"></path>
        </svg>
        Alternative Solution Approaches
      </h3>
      
      <Button
        onClick={generateAlternatives}
        disabled={isGenerating || !originalCode}
        className="w-full bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white"
      >
        {isGenerating ? (
          <div className="flex items-center">
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Generating Alternatives...
          </div>
        ) : (
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"></path>
            </svg>
            Generate Alternative Approaches
          </div>
        )}
      </Button>
      
      {alternativeSolutions.length > 0 && (
        <div className="mt-4">
          <div className="flex space-x-2 mb-3 overflow-x-auto pb-2">
            {alternativeSolutions.map(solution => (
              <button
                key={solution.id}
                onClick={() => setSelectedSolution(solution.id)}
                className={`px-3 py-1.5 rounded-md text-xs font-medium whitespace-nowrap transition-colors ${
                  selectedSolution === solution.id 
                    ? 'bg-amber-600 text-white' 
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {solution.approach}
              </button>
            ))}
          </div>
          
          {selectedSolutionData && (
            <div className="space-y-3">
              <div className="p-3 bg-gray-900/70 rounded-md border border-gray-700">
                <div className="text-xs text-amber-400 mb-1 font-medium">
                  {selectedSolutionData.approach} Implementation:
                </div>
                <pre className="text-xs text-gray-300 overflow-x-auto p-2 bg-black/30 rounded-md">
                  {selectedSolutionData.code}
                </pre>
              </div>
              
              <div className="grid grid-cols-2 gap-2">
                <div className="p-2 bg-gray-900/70 rounded-md border border-gray-700">
                  <div className="text-xs text-green-400 mb-1 font-medium">Time Complexity</div>
                  <div className="text-sm text-white">{selectedSolutionData.timeComplexity}</div>
                </div>
                <div className="p-2 bg-gray-900/70 rounded-md border border-gray-700">
                  <div className="text-xs text-blue-400 mb-1 font-medium">Space Complexity</div>
                  <div className="text-sm text-white">{selectedSolutionData.spaceComplexity}</div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-2">
                <div className="p-2 bg-gray-900/70 rounded-md border border-gray-700">
                  <div className="text-xs text-green-400 mb-1 font-medium">Pros</div>
                  <ul className="text-xs text-gray-300 list-disc pl-4 space-y-1">
                    {selectedSolutionData.pros.map((pro, index) => (
                      <li key={index}>{pro}</li>
                    ))}
                  </ul>
                </div>
                <div className="p-2 bg-gray-900/70 rounded-md border border-gray-700">
                  <div className="text-xs text-red-400 mb-1 font-medium">Cons</div>
                  <ul className="text-xs text-gray-300 list-disc pl-4 space-y-1">
                    {selectedSolutionData.cons.map((con, index) => (
                      <li key={index}>{con}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
      
      <div className="mt-2 text-xs text-gray-400 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1 text-yellow-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
        </svg>
        Premium feature: Compare different solution approaches
      </div>
    </div>
  );
};

export default CodeComparison;
