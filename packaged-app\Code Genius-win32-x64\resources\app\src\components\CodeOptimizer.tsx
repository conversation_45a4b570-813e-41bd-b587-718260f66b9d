import React, { useState } from 'react';
import { Button } from './ui/button';

interface CodeOptimizerProps {
  code: string;
  language: string;
  onOptimizedCode: (optimizedCode: string) => void;
}

const CodeOptimizer: React.FC<CodeOptimizerProps> = ({ code, language, onOptimizedCode }) => {
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizationLevel, setOptimizationLevel] = useState<'time' | 'space' | 'readability'>('time');

  const optimizeCode = async () => {
    if (!code || isOptimizing) return;
    
    setIsOptimizing(true);
    
    try {
      // In a real implementation, this would call the Gemini API with specific optimization prompts
      // For now, we'll simulate the optimization with a timeout
      setTimeout(() => {
        let optimizedCode = code;
        
        // Simple simulation of optimization
        if (optimizationLevel === 'time') {
          // Add time complexity optimization comments
          optimizedCode = `// Time-optimized solution
// Time Complexity: Improved from O(n²) to O(n log n)
// Used more efficient algorithm pattern

${code.replace(/for\s*\(\s*let\s+i\s*=\s*0/g, '// Replaced nested loop with more efficient approach\nfor (let i = 0')}`;
        } else if (optimizationLevel === 'space') {
          // Add space complexity optimization comments
          optimizedCode = `// Space-optimized solution
// Space Complexity: Improved from O(n) to O(1)
// Used in-place algorithm to reduce memory usage

${code.replace(/new Array/g, '// Avoided extra array allocation\n// new Array')}`;
        } else {
          // Add readability optimization
          optimizedCode = `// Enhanced for readability
// Added meaningful variable names and comments
// Restructured for better code organization

${code.split('\n').map(line => {
  // Add comments to explain complex parts
  if (line.includes('function') || line.includes('for') || line.includes('while') || line.includes('if')) {
    return line + ' // Purpose: Handles the main logic flow';
  }
  return line;
}).join('\n')}`;
        }
        
        onOptimizedCode(optimizedCode);
        setIsOptimizing(false);
      }, 1500);
    } catch (error) {
      console.error('Error optimizing code:', error);
      setIsOptimizing(false);
    }
  };

  return (
    <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700 mb-4">
      <h3 className="text-sm font-medium text-blue-400 mb-3 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M18 10h-4v4h4v-4z"></path>
          <path d="M10 10H6v4h4v-4z"></path>
          <path d="M18 6h-4v4h4V6z"></path>
          <path d="M10 6H6v4h4V6z"></path>
          <path d="M18 14h-4v4h4v-4z"></path>
          <path d="M10 14H6v4h4v-4z"></path>
        </svg>
        AI Code Optimizer
      </h3>
      
      <div className="mb-3">
        <div className="text-xs text-gray-400 mb-2">Optimization Target:</div>
        <div className="flex space-x-2">
          <button
            onClick={() => setOptimizationLevel('time')}
            className={`px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
              optimizationLevel === 'time' 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            Time Complexity
          </button>
          <button
            onClick={() => setOptimizationLevel('space')}
            className={`px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
              optimizationLevel === 'space' 
                ? 'bg-green-600 text-white' 
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            Space Complexity
          </button>
          <button
            onClick={() => setOptimizationLevel('readability')}
            className={`px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
              optimizationLevel === 'readability' 
                ? 'bg-purple-600 text-white' 
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            Readability
          </button>
        </div>
      </div>
      
      <Button
        onClick={optimizeCode}
        disabled={isOptimizing || !code}
        className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white"
      >
        {isOptimizing ? (
          <div className="flex items-center">
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Optimizing Code...
          </div>
        ) : (
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"></path>
            </svg>
            Optimize Solution
          </div>
        )}
      </Button>
      
      <div className="mt-2 text-xs text-gray-400 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1 text-yellow-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
        </svg>
        Premium feature: AI-powered code optimization for better performance
      </div>
    </div>
  );
};

export default CodeOptimizer;
