import React, { useState } from 'react';
import { COMMAND_KEY } from '../utils/platform';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();
  const [showShortcuts, setShowShortcuts] = useState(false);

  return (
    <footer className="bg-gray-900 border-t border-gray-800 py-3 px-4 text-xs text-gray-400">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <span>© {currentYear} Code Genius</span>

          <div className="flex items-center space-x-3">
            <button
              className="hover:text-gray-200 transition-colors"
              onClick={() => window.electronAPI.openExternal('https://codegenius.com/privacy')}
            >
              Privacy
            </button>
            <button
              className="hover:text-gray-200 transition-colors"
              onClick={() => window.electronAPI.openExternal('https://codegenius.com/terms')}
            >
              Terms
            </button>
            <button
              className="hover:text-gray-200 transition-colors"
              onClick={() => window.electronAPI.openExternal('https://codegenius.com/support')}
            >
              Support
            </button>
          </div>
        </div>

        <div className="relative">
          <button
            className="flex items-center space-x-2 hover:text-gray-200 transition-colors"
            onClick={() => setShowShortcuts(!showShortcuts)}
          >
            <span className="text-gray-500">Keyboard shortcuts</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-3 w-3 transition-transform ${showShortcuts ? 'rotate-180' : ''}`}
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <polyline points="6 9 12 15 18 9"></polyline>
            </svg>
          </button>

          {showShortcuts && (
            <div className="absolute bottom-full right-0 mb-2 bg-gray-800 border border-gray-700 rounded-lg shadow-lg p-3 w-64 z-10">
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="col-span-2 font-medium text-gray-300 mb-1">Window Controls</div>
                <div className="flex items-center">
                  <span className="text-gray-400">Toggle Window</span>
                </div>
                <div className="flex items-center justify-end">
                  <kbd className="px-1.5 py-0.5 bg-gray-700 border border-gray-600 rounded text-gray-300 mr-1">{COMMAND_KEY}</kbd>
                  <kbd className="px-1.5 py-0.5 bg-gray-700 border border-gray-600 rounded text-gray-300">B</kbd>
                </div>
                <div className="flex items-center">
                  <span className="text-gray-400">Move Window</span>
                </div>
                <div className="flex items-center justify-end">
                  <kbd className="px-1.5 py-0.5 bg-gray-700 border border-gray-600 rounded text-gray-300 mr-1">{COMMAND_KEY}</kbd>
                  <kbd className="px-1.5 py-0.5 bg-gray-700 border border-gray-600 rounded text-gray-300">↑↓←→</kbd>
                </div>

                <div className="col-span-2 font-medium text-gray-300 mt-2 mb-1">Functionality</div>
                <div className="flex items-center">
                  <span className="text-gray-400">Take Screenshot</span>
                </div>
                <div className="flex items-center justify-end">
                  <kbd className="px-1.5 py-0.5 bg-gray-700 border border-gray-600 rounded text-gray-300 mr-1">{COMMAND_KEY}</kbd>
                  <kbd className="px-1.5 py-0.5 bg-gray-700 border border-gray-600 rounded text-gray-300">H</kbd>
                </div>
                <div className="flex items-center">
                  <span className="text-gray-400">Process Images</span>
                </div>
                <div className="flex items-center justify-end">
                  <kbd className="px-1.5 py-0.5 bg-gray-700 border border-gray-600 rounded text-gray-300 mr-1">{COMMAND_KEY}</kbd>
                  <kbd className="px-1.5 py-0.5 bg-gray-700 border border-gray-600 rounded text-gray-300">Enter</kbd>
                </div>
                <div className="flex items-center">
                  <span className="text-gray-400">Reset View</span>
                </div>
                <div className="flex items-center justify-end">
                  <kbd className="px-1.5 py-0.5 bg-gray-700 border border-gray-600 rounded text-gray-300 mr-1">{COMMAND_KEY}</kbd>
                  <kbd className="px-1.5 py-0.5 bg-gray-700 border border-gray-600 rounded text-gray-300">R</kbd>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </footer>
  );
};

export default Footer;
