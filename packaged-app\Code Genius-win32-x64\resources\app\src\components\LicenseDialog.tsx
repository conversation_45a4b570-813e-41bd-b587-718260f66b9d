import React, { useState, useEffect } from 'react';
import { Button } from './ui/button';

interface LicenseDialogProps {
  onClose: () => void;
}

const LicenseDialog: React.FC<LicenseDialogProps> = ({ onClose }) => {
  const [licenseKey, setLicenseKey] = useState('');
  const [isActivating, setIsActivating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [licenseStatus, setLicenseStatus] = useState<{
    licensed: boolean;
    trialActive: boolean;
    trialDaysLeft: number | null;
    licenseKey: string | null;
  } | null>(null);

  useEffect(() => {
    // Get the current license status when the component mounts
    const getLicenseStatus = async () => {
      try {
        const status = await window.electronAPI.getLicenseStatus();
        setLicenseStatus(status);
      } catch (error) {
        console.error('Error getting license status:', error);
      }
    };

    getLicenseStatus();
  }, []);

  const handleActivate = async () => {
    if (!licenseKey.trim()) {
      setError('Please enter a valid license key');
      return;
    }

    setIsActivating(true);
    setError(null);

    try {
      const result = await window.electronAPI.activateLicense(licenseKey);
      
      if (result.success) {
        // Refresh license status
        const status = await window.electronAPI.getLicenseStatus();
        setLicenseStatus(status);
        setError(null);
      } else {
        setError(result.error || 'Failed to activate license');
      }
    } catch (error) {
      console.error('Error activating license:', error);
      setError('An unexpected error occurred');
    } finally {
      setIsActivating(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg shadow-xl border border-gray-700 p-6 max-w-md w-full">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-white flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-amber-500" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"/>
            </svg>
            Code Genius License
          </h2>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M18 6L6 18M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="mb-6">
          {licenseStatus?.licensed ? (
            <div className="bg-green-900/30 border border-green-700 rounded-md p-4 mb-4">
              <div className="flex items-center text-green-400 mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                  <polyline points="22 4 12 14.01 9 11.01" />
                </svg>
                <span className="font-medium">Licensed Version</span>
              </div>
              <p className="text-gray-300 text-sm">
                Thank you for purchasing Code Genius! You have full access to all premium features.
              </p>
              {licenseStatus.licenseKey && (
                <div className="mt-2 text-xs text-gray-400">
                  License Key: {licenseStatus.licenseKey.substring(0, 4)}...{licenseStatus.licenseKey.substring(licenseStatus.licenseKey.length - 4)}
                </div>
              )}
            </div>
          ) : licenseStatus?.trialActive ? (
            <div className="bg-blue-900/30 border border-blue-700 rounded-md p-4 mb-4">
              <div className="flex items-center text-blue-400 mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="10" />
                  <polyline points="12 6 12 12 16 14" />
                </svg>
                <span className="font-medium">Trial Version</span>
              </div>
              <p className="text-gray-300 text-sm">
                You are currently using the trial version of Code Genius.
                {licenseStatus.trialDaysLeft !== null && (
                  <span className="font-medium"> {licenseStatus.trialDaysLeft} days remaining.</span>
                )}
              </p>
            </div>
          ) : (
            <div className="bg-red-900/30 border border-red-700 rounded-md p-4 mb-4">
              <div className="flex items-center text-red-400 mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="10" />
                  <line x1="12" y1="8" x2="12" y2="12" />
                  <line x1="12" y1="16" x2="12.01" y2="16" />
                </svg>
                <span className="font-medium">Trial Expired</span>
              </div>
              <p className="text-gray-300 text-sm">
                Your trial period has expired. Please purchase a license to continue using Code Genius.
              </p>
            </div>
          )}
        </div>

        {!licenseStatus?.licensed && (
          <>
            <div className="mb-4">
              <label htmlFor="license-key" className="block text-sm font-medium text-gray-300 mb-1">
                License Key
              </label>
              <input
                id="license-key"
                type="text"
                value={licenseKey}
                onChange={(e) => setLicenseKey(e.target.value)}
                placeholder="Enter your license key"
                className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {error && (
                <p className="mt-1 text-sm text-red-400">{error}</p>
              )}
            </div>

            <div className="flex justify-between items-center">
              <Button
                onClick={handleActivate}
                disabled={isActivating || !licenseKey.trim()}
                className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-black font-medium py-2 px-4 rounded-md flex items-center"
              >
                {isActivating ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Activating...
                  </>
                ) : (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"/>
                    </svg>
                    Activate License
                  </>
                )}
              </Button>

              <a 
                href="#" 
                onClick={(e) => {
                  e.preventDefault();
                  window.electronAPI.openExternal('https://codegenius.com/purchase');
                }}
                className="text-blue-400 hover:text-blue-300 text-sm"
              >
                Purchase a license
              </a>
            </div>
          </>
        )}

        {licenseStatus?.licensed && (
          <Button
            onClick={onClose}
            className="w-full bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-md"
          >
            Close
          </Button>
        )}
      </div>
    </div>
  );
};

export default LicenseDialog;
