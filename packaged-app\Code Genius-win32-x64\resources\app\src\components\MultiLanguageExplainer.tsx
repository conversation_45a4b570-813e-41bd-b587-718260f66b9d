import React, { useState } from 'react';
import { Button } from './ui/button';

interface MultiLanguageExplainerProps {
  code: string;
  language: string;
}

const MultiLanguageExplainer: React.FC<MultiLanguageExplainerProps> = ({ code, language }) => {
  const [isExplaining, setIsExplaining] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState<string>('english');
  const [explanation, setExplanation] = useState<string | null>(null);
  
  const languages = [
    { id: 'english', name: 'English' },
    { id: 'spanish', name: 'Spanish' },
    { id: 'french', name: 'French' },
    { id: 'german', name: 'German' },
    { id: 'chinese', name: 'Chinese' },
    { id: 'japanese', name: 'Japanese' },
    { id: 'russian', name: 'Russian' },
    { id: 'arabic', name: 'Arabic' },
  ];

  const explainCode = async () => {
    if (!code || isExplaining) return;
    
    setIsExplaining(true);
    setExplanation(null);
    
    try {
      // In a real implementation, this would call the Gemini API with specific language prompts
      // For now, we'll simulate the explanation with a timeout
      setTimeout(() => {
        let simulatedExplanation = '';
        
        switch (selectedLanguage) {
          case 'english':
            simulatedExplanation = `This code implements a solution to the problem using a ${language === 'javascript' ? 'JavaScript' : language} approach. 
            
The algorithm works by:
1. First initializing the necessary variables
2. Then iterating through the input data
3. Applying the core logic to solve the problem
4. Finally returning the calculated result

The time complexity is O(n) where n is the size of the input, and the space complexity is O(1) as we're using constant extra space.`;
            break;
          case 'spanish':
            simulatedExplanation = `Este código implementa una solución al problema utilizando un enfoque de ${language === 'javascript' ? 'JavaScript' : language}.

El algoritmo funciona:
1. Primero inicializando las variables necesarias
2. Luego iterando a través de los datos de entrada
3. Aplicando la lógica principal para resolver el problema
4. Finalmente devolviendo el resultado calculado

La complejidad temporal es O(n) donde n es el tamaño de la entrada, y la complejidad espacial es O(1) ya que estamos usando espacio extra constante.`;
            break;
          case 'french':
            simulatedExplanation = `Ce code implémente une solution au problème en utilisant une approche ${language === 'javascript' ? 'JavaScript' : language}.

L'algorithme fonctionne en:
1. Initialisant d'abord les variables nécessaires
2. Puis en itérant à travers les données d'entrée
3. En appliquant la logique principale pour résoudre le problème
4. Enfin en retournant le résultat calculé

La complexité temporelle est O(n) où n est la taille de l'entrée, et la complexité spatiale est O(1) car nous utilisons un espace supplémentaire constant.`;
            break;
          case 'german':
            simulatedExplanation = `Dieser Code implementiert eine Lösung für das Problem mit einem ${language === 'javascript' ? 'JavaScript' : language}-Ansatz.

Der Algorithmus funktioniert durch:
1. Zuerst werden die notwendigen Variablen initialisiert
2. Dann wird durch die Eingabedaten iteriert
3. Die Kernlogik wird angewendet, um das Problem zu lösen
4. Schließlich wird das berechnete Ergebnis zurückgegeben

Die Zeitkomplexität ist O(n), wobei n die Größe der Eingabe ist, und die Raumkomplexität ist O(1), da wir konstanten zusätzlichen Speicherplatz verwenden.`;
            break;
          default:
            simulatedExplanation = `Explanation in ${languages.find(l => l.id === selectedLanguage)?.name || selectedLanguage} would be provided here in a real implementation.

This is a premium feature that uses AI to translate technical explanations into multiple languages while preserving the technical accuracy.`;
        }
        
        setExplanation(simulatedExplanation);
        setIsExplaining(false);
      }, 1500);
    } catch (error) {
      console.error('Error explaining code:', error);
      setIsExplaining(false);
    }
  };

  return (
    <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700 mb-4">
      <h3 className="text-sm font-medium text-purple-400 mb-3 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
        </svg>
        Multi-Language Explainer
      </h3>
      
      <div className="mb-3">
        <div className="text-xs text-gray-400 mb-2">Select Language:</div>
        <div className="grid grid-cols-4 gap-2">
          {languages.slice(0, 8).map(lang => (
            <button
              key={lang.id}
              onClick={() => setSelectedLanguage(lang.id)}
              className={`px-2 py-1.5 rounded-md text-xs font-medium transition-colors ${
                selectedLanguage === lang.id 
                  ? 'bg-purple-600 text-white' 
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              {lang.name}
            </button>
          ))}
        </div>
      </div>
      
      <Button
        onClick={explainCode}
        disabled={isExplaining || !code}
        className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
      >
        {isExplaining ? (
          <div className="flex items-center">
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Generating Explanation...
          </div>
        ) : (
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
            </svg>
            Explain in {languages.find(l => l.id === selectedLanguage)?.name}
          </div>
        )}
      </Button>
      
      {explanation && (
        <div className="mt-4 p-3 bg-gray-900/70 rounded-md border border-gray-700">
          <div className="text-xs text-purple-400 mb-1 font-medium">
            Explanation in {languages.find(l => l.id === selectedLanguage)?.name}:
          </div>
          <div className="text-sm text-gray-300 whitespace-pre-line">
            {explanation}
          </div>
        </div>
      )}
      
      <div className="mt-2 text-xs text-gray-400 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1 text-yellow-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
        </svg>
        Premium feature: Explains code in multiple languages
      </div>
    </div>
  );
};

export default MultiLanguageExplainer;
