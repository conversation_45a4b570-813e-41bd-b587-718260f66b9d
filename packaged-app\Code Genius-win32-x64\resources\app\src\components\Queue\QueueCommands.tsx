import React, { useState, useEffect, useRef } from "react"

import { supabase } from "../../lib/supabase"
import { useToast } from "../../contexts/toast"
import { LanguageSelector } from "../shared/LanguageSelector"
import { COMMAND_KEY } from "../../utils/platform"

// Note: The 'declare global { interface Window { ... } }' block previously here
// has been REMOVED. Ensure the type definition for 'window.electronAPI' exists
// and is correct in your dedicated 'src/global.d.ts' file.

interface QueueCommandsProps {
  onTooltipVisibilityChange: (visible: boolean, height: number) => void
  screenshotCount?: number
  credits: number
  currentLanguage: string
  setLanguage: (language: string) => void
}

const QueueCommands: React.FC<QueueCommandsProps> = ({
  onTooltipVisibilityChange,
  screenshotCount = 0,
  credits,
  currentLanguage,
  setLanguage
}) => {
  const [isTooltipVisible, setIsTooltipVisible] = useState(false)
  const tooltipRef = useRef<HTMLDivElement>(null)
  const { showToast } = useToast()

  useEffect(() => {
    let tooltipHeight = 0
    if (tooltipRef.current && isTooltipVisible) {
      // Add a buffer (e.g., 10px) for spacing below the tooltip
      tooltipHeight = tooltipRef.current.offsetHeight + 10
    }
    onTooltipVisibilityChange(isTooltipVisible, tooltipHeight)

    // Add keyboard event listener to close tooltip with Escape key
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isTooltipVisible) {
        setIsTooltipVisible(false)
      }
    }

    window.addEventListener('keydown', handleKeyDown)

    // Clean up event listener
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [isTooltipVisible, onTooltipVisibilityChange])

  const handleSignOut = async () => {
    try {
      // Clear any local storage or electron-specific data first
      localStorage.clear()
      sessionStorage.clear()
      // Potentially add electron-specific storage clear if needed:
      // await window.electronAPI?.clearStorage?.(); // Example

      // Then sign out from Supabase
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      // Optionally trigger a page reload or redirect after sign out
      // window.location.reload();
    } catch (err) {
      console.error("Error signing out:", err)
      showToast("Error", "Failed to sign out", "error") // Show error to user
    }
  }

  const handleMouseEnter = () => {
    setIsTooltipVisible(true)
  }

  const handleMouseLeave = () => {
    setIsTooltipVisible(false)
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {/* Screenshot Button */}
      <button
        className="flex items-center justify-center gap-2 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 text-white rounded-lg py-3 px-4 transition-all duration-300 shadow-md hover:shadow-lg"
        onClick={async () => {
          try {
            // Ensure electronAPI exists before calling
            if (window.electronAPI?.triggerScreenshot) {
                const result = await window.electronAPI.triggerScreenshot()
                if (!result.success) {
                  console.error("Failed to take screenshot:", result.error)
                  showToast("Error", `Failed to take screenshot: ${result.error || 'Unknown error'}`, "error")
                }
            } else {
                console.error("electronAPI.triggerScreenshot not available");
                showToast("Error", "Screenshot functionality not available", "error");
            }
          } catch (error) {
            console.error("Error taking screenshot:", error)
            showToast("Error", "An unexpected error occurred while taking screenshot", "error")
          }
        }}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
          <circle cx="8.5" cy="8.5" r="1.5"></circle>
          <polyline points="21 15 16 10 5 21"></polyline>
        </svg>
        <span className="font-medium">
          {screenshotCount === 0
            ? "Take First Screenshot"
            : screenshotCount === 1
            ? "Take Second Screenshot"
            : "Reset Screenshots"}
        </span>
        <div className="ml-auto flex items-center gap-1 opacity-70">
          <kbd className="px-1.5 py-0.5 bg-black/30 rounded text-xs">{COMMAND_KEY}</kbd>
          <kbd className="px-1.5 py-0.5 bg-black/30 rounded text-xs">H</kbd>
        </div>
      </button>

      {/* Solve Button */}
      <button
        className={`flex items-center justify-center gap-2 rounded-lg py-3 px-4 transition-all duration-300 shadow-md hover:shadow-lg font-medium ${
          screenshotCount > 0
            ? "bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white"
            : "bg-gray-700 text-gray-400 cursor-not-allowed"
        }`}
        disabled={screenshotCount === 0}
        onClick={async () => {
          if (screenshotCount === 0) return;

          try {
             // Ensure electronAPI exists before calling
             if (window.electronAPI?.triggerProcessScreenshots) {
                const result = await window.electronAPI.triggerProcessScreenshots()
                if (!result.success) {
                  console.error("Failed to process screenshots:", result.error)
                  showToast("Error", `Failed to process screenshots: ${result.error || 'Unknown error'}`, "error")
                }
             } else {
                 console.error("electronAPI.triggerProcessScreenshots not available");
                 showToast("Error", "Processing functionality not available", "error");
             }
          } catch (error) {
            console.error("Error processing screenshots:", error)
            showToast("Error", "An unexpected error occurred while processing screenshots", "error")
          }
        }}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <polyline points="9 18 15 12 9 6"></polyline>
        </svg>
        <span>
          {screenshotCount === 0 ? "Take Screenshot First" : "Generate Solution"}
        </span>
        {screenshotCount > 0 && (
          <div className="ml-auto flex items-center gap-1 opacity-70">
            <kbd className="px-1.5 py-0.5 bg-black/30 rounded text-xs">{COMMAND_KEY}</kbd>
            <kbd className="px-1.5 py-0.5 bg-black/30 rounded text-xs">↵</kbd>
          </div>
        )}
      </button>

      {/* Settings with Tooltip */}
      <div className="col-span-1 md:col-span-2 mt-2">
        <div
          className="relative inline-block"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          {/* Settings button */}
          <button className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors text-sm">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="w-4 h-4"
            >
              <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
              <circle cx="12" cy="12" r="3" />
            </svg>
            <span>Settings & Keyboard Shortcuts</span>
          </button>

          {/* Tooltip Content */}
          {isTooltipVisible && (
            <div
              ref={tooltipRef}
              className="absolute bottom-full left-1/2 mb-2 w-96 transform -translate-x-1/2" // Positioned above, centered
              style={{ zIndex: 100 }}
            >
                {/* Optional: Add a small triangle pointer */}
                {/* <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-x-8 border-x-transparent border-t-8 border-t-gray-800"></div> */}

                <div className="p-4 text-sm bg-gray-800 rounded-lg border border-gray-700 text-white shadow-xl">
                {/* Close button */}
                <button
                  onClick={() => setIsTooltipVisible(false)}
                  className="absolute top-2 right-2 text-gray-400 hover:text-white p-1 rounded-full hover:bg-gray-700/50 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M18 6L6 18M6 6l12 12" />
                  </svg>
                </button>
                <div className="space-y-5">
                  {/* Keyboard Shortcuts Section */}
                  <div>
                    <h3 className="font-medium text-base mb-3 text-blue-400 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                        <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                      </svg>
                      <span>Keyboard Shortcuts</span>
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 glass-light rounded-lg p-4 border-glow-blue">
                      {/* Toggle Command */}
                      <div
                        className="cursor-pointer rounded-lg px-3 py-2.5 hover:glass-blue transition-colors border border-transparent hover:border-glow-blue flex flex-col"
                        onClick={async () => {
                          try {
                            if (window.electronAPI?.toggleMainWindow) {
                                const result = await window.electronAPI.toggleMainWindow()
                                if (!result.success) {
                                  console.error("Failed to toggle window:", result.error)
                                  showToast("Error", `Failed to toggle window: ${result.error || 'Unknown error'}`, "error")
                                }
                            } else {
                                console.error("electronAPI.toggleMainWindow not available");
                                showToast("Error", "Window toggle functionality not available", "error");
                            }
                          } catch (error) {
                            console.error("Error toggling window:", error)
                            showToast("Error", "An unexpected error occurred while toggling window", "error")
                          }
                        }}
                      >
                        <div className="flex items-center justify-between mb-1.5">
                          <span className="font-medium text-white">Toggle Window</span>
                          <div className="flex gap-1 flex-shrink-0 glass-blue border-glow-blue px-2 py-1 rounded-md">
                            <kbd className="text-blue-300 text-xs font-mono">
                              {COMMAND_KEY}
                            </kbd>
                            <span className="text-gray-500">+</span>
                            <kbd className="text-blue-300 text-xs font-mono">
                              B
                            </kbd>
                          </div>
                        </div>
                        <p className="text-xs text-gray-400">
                          Show or hide this window
                        </p>
                      </div>

                      {/* Screenshot Command */}
                      <div
                        className="cursor-pointer rounded-lg px-3 py-2.5 hover:glass-blue transition-colors border border-transparent hover:border-glow-blue flex flex-col"
                        onClick={async () => {
                          try {
                            if (window.electronAPI?.triggerScreenshot) {
                                const result = await window.electronAPI.triggerScreenshot()
                                if (!result.success) {
                                  console.error("Failed to take screenshot:", result.error)
                                  showToast("Error", `Failed to take screenshot: ${result.error || 'Unknown error'}`, "error")
                                }
                            } else {
                                console.error("electronAPI.triggerScreenshot not available");
                                showToast("Error", "Screenshot functionality not available", "error");
                            }
                          } catch (error) {
                            console.error("Error taking screenshot:", error)
                            showToast("Error", "An unexpected error occurred while taking screenshot", "error")
                          }
                        }}
                      >
                        <div className="flex items-center justify-between mb-1.5">
                          <span className="font-medium text-white">Take Screenshot</span>
                          <div className="flex gap-1 flex-shrink-0 glass-green border-glow-green px-2 py-1 rounded-md">
                            <kbd className="text-green-300 text-xs font-mono">
                              {COMMAND_KEY}
                            </kbd>
                            <span className="text-gray-500">+</span>
                            <kbd className="text-green-300 text-xs font-mono">
                              H
                            </kbd>
                          </div>
                        </div>
                        <p className="text-xs text-gray-400">
                          Capture problem description
                        </p>
                      </div>

                      {/* Solve Command */}
                      <div
                        className={`cursor-pointer rounded-lg px-3 py-2.5 hover:glass-purple transition-colors border border-transparent hover:border-glow-purple flex flex-col ${
                          screenshotCount > 0
                            ? ""
                            : "opacity-60 cursor-not-allowed"
                        }`}
                        onClick={async () => {
                          if (screenshotCount === 0) return

                          try {
                            if (window.electronAPI?.triggerProcessScreenshots) {
                                const result = await window.electronAPI.triggerProcessScreenshots()
                                if (!result.success) {
                                  console.error("Failed to process screenshots:", result.error)
                                  showToast("Error", `Failed to process screenshots: ${result.error || 'Unknown error'}`, "error")
                                }
                            } else {
                                console.error("electronAPI.triggerProcessScreenshots not available");
                                showToast("Error", "Processing functionality not available", "error");
                            }
                          } catch (error) {
                            console.error("Error processing screenshots:", error)
                            showToast("Error", "An unexpected error occurred while processing screenshots", "error")
                          }
                        }}
                      >
                        <div className="flex items-center justify-between mb-1.5">
                          <span className="font-medium text-white">Generate Solution</span>
                          <div className="flex gap-1 flex-shrink-0 glass-purple border-glow-purple px-2 py-1 rounded-md">
                            <kbd className="text-purple-300 text-xs font-mono">
                              {COMMAND_KEY}
                            </kbd>
                            <span className="text-gray-500">+</span>
                            <kbd className="text-purple-300 text-xs font-mono">
                              ↵
                            </kbd>
                          </div>
                        </div>
                        <p className="text-xs text-gray-400">
                          {screenshotCount > 0
                            ? "Process and solve the problem"
                            : "Take a screenshot first"}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Language Selection */}
                  <div>
                    <h3 className="font-medium text-base mb-3 text-purple-400 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                        <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                      </svg>
                      <span>Programming Language</span>
                    </h3>
                    <div className="glass-light rounded-lg p-4 border-glow-purple">
                      <LanguageSelector
                        currentLanguage={currentLanguage}
                        setLanguage={setLanguage}
                      />
                    </div>
                  </div>

                  {/* Credits Display */}
                  <div>
                    <h3 className="font-medium text-base mb-3 text-green-400 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8"></path>
                        <line x1="12" y1="6" x2="12" y2="18"></line>
                      </svg>
                      <span>Credits</span>
                    </h3>
                    <div className="glass-light rounded-lg p-4 border-glow-green relative overflow-hidden">
                      {/* Animated background glow */}
                      <div className="absolute -inset-1 bg-gradient-to-r from-green-500/10 via-blue-500/5 to-green-500/10 blur-xl animate-gradient-x"></div>

                      <div className="relative z-10">
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-gray-300 font-medium">Unlimited Credits</span>
                          <div className="bg-green-900/30 text-green-400 text-xs font-medium px-2 py-1 rounded-full flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M20 6L9 17l-5-5" />
                            </svg>
                            <span>Active</span>
                          </div>
                        </div>

                        <div className="w-full bg-gray-800 rounded-full h-3 p-0.5 mb-3">
                          <div className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full animate-pulse" style={{ width: '100%' }}></div>
                        </div>

                        <div className="flex items-center text-xs text-gray-400">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1 text-blue-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" />
                          </svg>
                          <span>Powered by Gemini API</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Account Actions */}
                  <div className="pt-4 mt-4 border-t border-blue-500/30">
                    <div className="grid grid-cols-2 gap-3">
                      <button
                        onClick={async () => { // Make async
                          try {
                            // 1. Get current user from Supabase
                            const { data: { user }, error: userError } = await supabase.auth.getUser();

                            if (userError || !user) {
                              console.error("Error getting user or user not logged in:", userError);
                              showToast("Error", "You must be logged in to manage your license.", "error");
                              return; // Exit if no user found
                            }

                            // 2. Check if the correct electronAPI function exists
                            if (window.electronAPI?.openSubscriptionPortal) {
                              // 3. Prepare the required authData
                              const authData = {
                                id: user.id,
                                email: user.email ?? '',
                              };

                              // 4. Call the function with the required argument
                              const result = await window.electronAPI.openSubscriptionPortal(authData);

                              // 5. Handle potential errors from the API call itself
                              if (!result.success) {
                                  console.error("Failed to open subscription portal:", result.error);
                                  showToast("Error", `Could not open portal: ${result.error || 'Unknown error'}`, "error");
                              }
                            } else {
                                console.error("electronAPI.openSubscriptionPortal not available");
                                showToast("Error", "Manage license functionality not available", "error");
                            }
                          } catch (error) {
                              console.error("Error preparing to open subscription portal:", error);
                              showToast("Error", "An unexpected error occurred while trying to manage license.", "error");
                          }
                        }}
                        className="glass-blue border-glow-blue rounded-lg py-2 px-3 transition-all duration-300 flex items-center justify-center group sci-fi-button"
                      >
                        <div className="p-1 rounded-full glass-blue neon-blue transition-colors mr-2">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"/>
                          </svg>
                        </div>
                        <span className="text-blue-300 neon-text-blue transition-colors text-sm font-medium">MANAGE LICENSE</span>
                      </button>

                      <button
                        onClick={handleSignOut}
                        className="glass-dark border-glow-red rounded-lg py-2 px-3 transition-all duration-300 flex items-center justify-center group sci-fi-button"
                      >
                        <div className="p-1 rounded-full glass-red neon-red transition-colors mr-2">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-red-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
                            <polyline points="16 17 21 12 16 7" />
                            <line x1="21" y1="12" x2="9" y2="12" />
                          </svg>
                        </div>
                        <span className="text-red-400 transition-colors text-sm font-medium">SIGN OUT</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default QueueCommands