import React from "react"
import ScreenshotItem from "./ScreenshotItem"
import { COMMAND_KEY } from "../../utils/platform"

interface Screenshot {
  path: string
  preview: string
}

interface ScreenshotQueueProps {
  isLoading: boolean
  screenshots: Screenshot[]
  onDeleteScreenshot: (index: number) => void
}
const ScreenshotQueue: React.FC<ScreenshotQueueProps> = ({
  isLoading,
  screenshots,
  onDeleteScreenshot
}) => {
  if (screenshots.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 px-4 bg-gray-800/30 rounded-lg border border-gray-700 text-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-600 mb-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
          <circle cx="8.5" cy="8.5" r="1.5"></circle>
          <polyline points="21 15 16 10 5 21"></polyline>
        </svg>
        <h3 className="text-gray-400 font-medium mb-1">No Screenshots Yet</h3>
        <p className="text-gray-500 text-sm max-w-xs">
          Press <kbd className="px-1.5 py-0.5 bg-gray-700 border border-gray-600 rounded text-gray-300 text-xs">{COMMAND_KEY}+B</kbd> to hide this window, then take a screenshot of a coding problem.
        </p>
      </div>
    )
  }

  const displayScreenshots = screenshots.slice(0, 5)

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
      {displayScreenshots.map((screenshot, index) => (
        <ScreenshotItem
          key={screenshot.path}
          isLoading={isLoading}
          screenshot={screenshot}
          index={index}
          onDelete={onDeleteScreenshot}
        />
      ))}
      {screenshots.length > 5 && (
        <div className="flex items-center justify-center bg-gray-800/50 rounded-lg border border-gray-700 p-4 text-center">
          <div className="text-gray-400 text-sm">
            <span className="font-medium text-white text-lg">+{screenshots.length - 5}</span>
            <div className="mt-1">additional screenshots</div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ScreenshotQueue
