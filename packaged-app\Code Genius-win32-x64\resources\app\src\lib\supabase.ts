import { createClient } from "@supabase/supabase-js"

console.log("Using mock Supabase client for free version")

// Use dummy values for Supabase URL and key since we're not using Supabase in the free version
const supabaseUrl = "https://example.com"
const supabaseAnonKey = "dummy-key"

// Create a mock Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    flowType: "pkce",
    detectSessionInUrl: true,
    persistSession: true,
    autoRefreshToken: true,
    debug: true,
    storage: {
      getItem: (key) => {
        const item = localStorage.getItem(key)
        console.log("Auth storage - Getting key:", key, "Value exists:", !!item)
        return item
      },
      setItem: (key, value) => {
        console.log("Auth storage - Setting key:", key)
        localStorage.setItem(key, value)
      },
      removeItem: (key) => {
        console.log("Auth storage - Removing key:", key)
        localStorage.removeItem(key)
      }
    }
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    },
    headers: {
      apikey: supabase<PERSON><PERSON><PERSON><PERSON>
    }
  }
})

// Mock functions for the free version
export const signInWithGoogle = async () => {
  console.log("Mock Google sign in for free version")
  return { url: "#" }
}

// No need for auth state monitoring in the free version
