// WebSpeechRecognition.ts
// This file implements browser-based speech recognition using the Web Speech API
// as a fallback when Deepgram is not available or fails

/**
 * Class for handling browser-based speech recognition using the Web Speech API
 */
export class WebSpeechRecognition {
  private recognition: SpeechRecognition | null = null;
  private isListening: boolean = false;
  private onResultCallback: ((transcript: string) => void) | null = null;
  private onErrorCallback: ((error: any) => void) | null = null;
  private continuous: boolean = false;
  private interimResults: boolean = true;
  private language: string = 'en-US';
  private finalTranscript: string = '';
  private interimTranscript: string = '';

  /**
   * Create a new WebSpeechRecognition instance
   */
  constructor() {
    this.initRecognition();
  }

  /**
   * Initialize the SpeechRecognition object
   */
  private initRecognition(): void {
    try {
      // Check if the browser supports the Web Speech API
      if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        console.error('Web Speech API is not supported in this browser');
        return;
      }

      // Create the SpeechRecognition object
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      this.recognition = new SpeechRecognition();

      // Configure the recognition
      if (this.recognition) {
        this.recognition.continuous = this.continuous;
        this.recognition.interimResults = this.interimResults;
        this.recognition.lang = this.language;

        // Set up event handlers
        this.recognition.onresult = this.handleResult.bind(this);
        this.recognition.onerror = this.handleError.bind(this);
        this.recognition.onend = this.handleEnd.bind(this);
      }
    } catch (error) {
      console.error('Error initializing Web Speech API:', error);
    }
  }

  /**
   * Handle speech recognition results
   * @param event The speech recognition result event
   */
  private handleResult(event: SpeechRecognitionEvent): void {
    this.interimTranscript = '';
    
    // Process the results
    for (let i = event.resultIndex; i < event.results.length; ++i) {
      if (event.results[i].isFinal) {
        this.finalTranscript += event.results[i][0].transcript;
      } else {
        this.interimTranscript += event.results[i][0].transcript;
      }
    }

    // Call the result callback with the current transcript
    if (this.onResultCallback) {
      this.onResultCallback(this.finalTranscript + this.interimTranscript);
    }
  }

  /**
   * Handle speech recognition errors
   * @param event The speech recognition error event
   */
  private handleError(event: SpeechRecognitionEvent): void {
    console.error('Speech recognition error:', event.error);
    
    if (this.onErrorCallback) {
      this.onErrorCallback(event.error);
    }

    // Restart recognition on error if we're supposed to be listening
    if (this.isListening && this.recognition) {
      try {
        this.recognition.stop();
        setTimeout(() => {
          if (this.isListening && this.recognition) {
            this.recognition.start();
          }
        }, 1000);
      } catch (e) {
        console.error('Error restarting speech recognition:', e);
      }
    }
  }

  /**
   * Handle speech recognition end event
   */
  private handleEnd(): void {
    // Restart recognition if we're supposed to be continuously listening
    if (this.isListening && this.continuous && this.recognition) {
      try {
        this.recognition.start();
      } catch (e) {
        console.error('Error restarting speech recognition after end:', e);
      }
    }
  }

  /**
   * Start listening for speech
   * @param continuous Whether to continuously listen
   * @param interimResults Whether to return interim results
   * @param language The language to recognize
   */
  public start(continuous: boolean = false, interimResults: boolean = true, language: string = 'en-US'): void {
    if (!this.recognition) {
      this.initRecognition();
    }

    if (!this.recognition) {
      console.error('Speech recognition is not supported');
      return;
    }

    try {
      // Update configuration
      this.continuous = continuous;
      this.interimResults = interimResults;
      this.language = language;
      this.recognition.continuous = continuous;
      this.recognition.interimResults = interimResults;
      this.recognition.lang = language;

      // Reset transcripts
      this.finalTranscript = '';
      this.interimTranscript = '';

      // Start recognition
      this.recognition.start();
      this.isListening = true;
      console.log('Web Speech Recognition started');
    } catch (error) {
      console.error('Error starting speech recognition:', error);
    }
  }

  /**
   * Stop listening for speech
   */
  public stop(): void {
    if (this.recognition && this.isListening) {
      try {
        this.recognition.stop();
        this.isListening = false;
        console.log('Web Speech Recognition stopped');
      } catch (error) {
        console.error('Error stopping speech recognition:', error);
      }
    }
  }

  /**
   * Set the callback for speech recognition results
   * @param callback The function to call with the transcript
   */
  public onResult(callback: (transcript: string) => void): void {
    this.onResultCallback = callback;
  }

  /**
   * Set the callback for speech recognition errors
   * @param callback The function to call with the error
   */
  public onError(callback: (error: any) => void): void {
    this.onErrorCallback = callback;
  }

  /**
   * Check if speech recognition is supported in this browser
   * @returns True if speech recognition is supported, false otherwise
   */
  public static isSupported(): boolean {
    return ('webkitSpeechRecognition' in window) || ('SpeechRecognition' in window);
  }
}

// Add TypeScript declarations for the Web Speech API
declare global {
  interface Window {
    SpeechRecognition: typeof SpeechRecognition;
    webkitSpeechRecognition: typeof SpeechRecognition;
  }
}
