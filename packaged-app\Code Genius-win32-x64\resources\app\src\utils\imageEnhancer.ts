/**
 * Image Enhancer Utility
 * 
 * This utility provides advanced image enhancement capabilities for screenshots
 * to improve the quality of code extraction and problem analysis.
 */

// Canvas-based image processing
const enhanceImage = async (imageDataUrl: string): Promise<string> => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      // Create canvas
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        console.error('Failed to get canvas context');
        resolve(imageDataUrl); // Return original if enhancement fails
        return;
      }
      
      // Set canvas dimensions
      canvas.width = img.width;
      canvas.height = img.height;
      
      // Draw original image
      ctx.drawImage(img, 0, 0);
      
      // Get image data for processing
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;
      
      // Apply advanced image processing
      applyImageProcessing(data, canvas.width, canvas.height);
      
      // Put processed image data back
      ctx.putImageData(imageData, 0, 0);
      
      // Return enhanced image as data URL
      resolve(canvas.toDataURL('image/png'));
    };
    
    img.src = imageDataUrl;
  });
};

// Apply various image processing techniques
const applyImageProcessing = (data: Uint8ClampedArray, width: number, height: number) => {
  // 1. Increase contrast for better text recognition
  increaseContrast(data);
  
  // 2. Sharpen edges for better code block detection
  sharpenEdges(data, width, height);
  
  // 3. Reduce noise
  reduceNoise(data, width, height);
  
  // 4. Optimize for text
  optimizeForText(data);
};

// Increase contrast to make text more readable
const increaseContrast = (data: Uint8ClampedArray) => {
  const factor = 1.2; // Contrast factor
  const intercept = 128 * (1 - factor);
  
  for (let i = 0; i < data.length; i += 4) {
    // Apply to RGB channels
    for (let j = 0; j < 3; j++) {
      data[i + j] = factor * data[i + j] + intercept;
    }
  }
};

// Sharpen edges to improve code block detection
const sharpenEdges = (data: Uint8ClampedArray, width: number, height: number) => {
  // Create a copy of the original data
  const original = new Uint8ClampedArray(data);
  
  // Kernel for sharpening
  const kernel = [
    0, -1, 0,
    -1, 5, -1,
    0, -1, 0
  ];
  
  // Apply convolution
  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      const pixelIndex = (y * width + x) * 4;
      
      // For each RGB channel
      for (let c = 0; c < 3; c++) {
        let sum = 0;
        
        // Apply kernel
        for (let ky = -1; ky <= 1; ky++) {
          for (let kx = -1; kx <= 1; kx++) {
            const kernelIndex = (ky + 1) * 3 + (kx + 1);
            const dataIndex = ((y + ky) * width + (x + kx)) * 4 + c;
            
            sum += original[dataIndex] * kernel[kernelIndex];
          }
        }
        
        // Clamp values
        data[pixelIndex + c] = Math.max(0, Math.min(255, sum));
      }
    }
  }
};

// Reduce noise to improve text clarity
const reduceNoise = (data: Uint8ClampedArray, width: number, height: number) => {
  // Create a copy of the original data
  const original = new Uint8ClampedArray(data);
  
  // Apply median filter (3x3)
  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      const pixelIndex = (y * width + x) * 4;
      
      // For each RGB channel
      for (let c = 0; c < 3; c++) {
        const values: number[] = [];
        
        // Collect values in 3x3 neighborhood
        for (let ky = -1; ky <= 1; ky++) {
          for (let kx = -1; kx <= 1; kx++) {
            const dataIndex = ((y + ky) * width + (x + kx)) * 4 + c;
            values.push(original[dataIndex]);
          }
        }
        
        // Sort and get median
        values.sort((a, b) => a - b);
        data[pixelIndex + c] = values[4]; // Middle value (median)
      }
    }
  }
};

// Optimize for text recognition
const optimizeForText = (data: Uint8ClampedArray) => {
  // Threshold for binarization
  const threshold = 150;
  
  for (let i = 0; i < data.length; i += 4) {
    // Calculate grayscale value
    const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
    
    // Apply soft thresholding for text
    const value = gray > threshold ? 255 : gray < threshold - 50 ? 0 : (gray - (threshold - 50)) * 5;
    
    // Set RGB channels
    data[i] = value;
    data[i + 1] = value;
    data[i + 2] = value;
  }
};

// Detect code blocks in the image
export const detectCodeBlocks = async (imageDataUrl: string): Promise<{
  hasCode: boolean;
  codeRegions: Array<{ x: number, y: number, width: number, height: number }>;
}> => {
  // This would use more advanced ML techniques in a real implementation
  // For now, we'll return a simplified result
  return {
    hasCode: true,
    codeRegions: [
      { x: 50, y: 100, width: 400, height: 300 }
    ]
  };
};

// Main export for image enhancement
export const enhanceScreenshot = async (imageDataUrl: string): Promise<string> => {
  try {
    console.log('Enhancing screenshot for better analysis...');
    const enhanced = await enhanceImage(imageDataUrl);
    console.log('Screenshot enhancement complete');
    return enhanced;
  } catch (error) {
    console.error('Error enhancing screenshot:', error);
    return imageDataUrl; // Return original if enhancement fails
  }
};

export default enhanceScreenshot;
